<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IC Memo Query Agent (<PERSON>)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 1200px;
            padding: 40px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1rem;
        }

        .input-area {
            margin-bottom: 30px;
        }

        .query-input {
            width: 100%;
            padding: 15px 20px;
            font-size: 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            transition: border-color 0.3s;
        }

        .query-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .suggestion {
            background: #f0f4ff;
            color: #667eea;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .suggestion:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 15px;
            transition: transform 0.2s;
        }

        .btn-primary:hover {
            transform: scale(1.02);
        }

        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .results {
            margin-top: 30px;
        }

        .sql-query {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .result-table th {
            background: #f8f9fa;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }

        .result-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .result-table tr:hover {
            background: #f8f9fa;
        }

        .error {
            background: #fee;
            color: #c00;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        .loading {
            text-align: center;
            color: #667eea;
            margin: 20px 0;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat {
            flex: 1;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 IC Memo Query Agent</h1>
        <p class="subtitle">Ask questions about investment committee memos in natural language</p>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat">
                <div class="stat-value" id="memo-count">0</div>
                <div class="stat-label">Memos Loaded</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="deal-count">0</div>
                <div class="stat-label">Deals Tracked</div>
            </div>
            <div class="stat">
                <div class="stat-value" id="query-count">0</div>
                <div class="stat-label">Queries Run</div>
            </div>
        </div>

        <div class="input-area">
            <input
                type="text"
                class="query-input"
                id="queryInput"
                placeholder="Ask a question about the investment memos..."
                autocomplete="off"
            />

            <div class="suggestions" id="suggestions">
                <!-- Suggestions will be loaded here -->
            </div>

            <button class="btn-primary" id="submitBtn" onclick="submitQuery()">
                Ask Question
            </button>
        </div>

        <div class="results" id="results">
            <!-- Results will appear here -->
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8000';
        let queryCount = 0;
        let conversationHistory = [];

        // Load initial data
        async function initialize() {
            try {
                // Get health check
                const healthResponse = await fetch(`${API_URL}/api/health`);
                if (healthResponse.ok) {
                    const health = await healthResponse.json();
                    document.getElementById('memo-count').textContent = health.memos_loaded || 0;
                    document.getElementById('deal-count').textContent = health.deals_tracked || 0;
                    document.getElementById('stats').style.display = 'flex';
                }

                // Get schema and suggestions
                const schemaResponse = await fetch(`${API_URL}/api/schema`);
                if (schemaResponse.ok) {
                    const schema = await schemaResponse.json();
                    displaySuggestions(schema.sample_queries || []);
                }
            } catch (error) {
                console.error('Failed to initialize:', error);
            }
        }

        function displaySuggestions(queries) {
            const container = document.getElementById('suggestions');
            container.innerHTML = queries.slice(0, 5).map(q =>
                `<button class="suggestion" onclick="useSuggestion('${q.replace(/'/g, "\\'")}')">${q}</button>`
            ).join('');
        }

        function useSuggestion(query) {
            document.getElementById('queryInput').value = query;
            submitQuery();
        }

        async function submitQuery() {
            const input = document.getElementById('queryInput');
            const question = input.value.trim();

            if (!question) {
                alert('Please enter a question');
                return;
            }

            const resultsDiv = document.getElementById('results');
            const submitBtn = document.getElementById('submitBtn');

            // Show loading
            submitBtn.disabled = true;
            resultsDiv.innerHTML = '<div class="loading">Analyzing your question...</div>';

            try {
                const response = await fetch(`${API_URL}/api/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        model: 'gpt-4o-mini',
                        conversation_history: conversationHistory.slice(-3)
                    })
                });

                const result = await response.json();

                if (result.error) {
                    resultsDiv.innerHTML = `<div class="error">Error: ${result.error}</div>`;
                } else {
                    displayResults(result);

                    // Update conversation history
                    conversationHistory.push({
                        question: question,
                        sql: result.sql,
                        results: result.results
                    });

                    // Update query count
                    queryCount++;
                    document.getElementById('query-count').textContent = queryCount;
                }

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Failed to process query: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
            }
        }

        function displayResults(result) {
            const resultsDiv = document.getElementById('results');

            let html = '';

            // Show SQL query
            if (result.sql) {
                html += `<h3>Generated SQL:</h3>`;
                html += `<div class="sql-query">${result.sql}</div>`;
            }

            // Show results table
            if (result.results && result.results.length > 0) {
                html += `<h3>Results (${result.results.length} rows):</h3>`;
                html += '<div style="overflow-x: auto;">';
                html += '<table class="result-table">';

                // Headers
                const columns = Object.keys(result.results[0]);
                html += '<thead><tr>';
                columns.forEach(col => {
                    html += `<th>${col}</th>`;
                });
                html += '</tr></thead>';

                // Data
                html += '<tbody>';
                result.results.forEach(row => {
                    html += '<tr>';
                    columns.forEach(col => {
                        let value = row[col];
                        if (value === null) value = 'NULL';
                        else if (typeof value === 'object') value = JSON.stringify(value);
                        html += `<td>${value}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody>';
                html += '</table>';
                html += '</div>';
            } else {
                html += '<div class="error">No results found</div>';
            }

            resultsDiv.innerHTML = html;
        }

        // Allow Enter key to submit
        document.getElementById('queryInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                submitQuery();
            }
        });

        // Initialize on load
        window.addEventListener('load', initialize);
    </script>
</body>
</html>
