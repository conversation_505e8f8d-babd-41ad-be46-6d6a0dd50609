"""
(<PERSON>) FastAPI server for IC Memo NL-to-SQL interface
Provides endpoints for natural language queries on investment committee data.
"""

import sqlite3
import traceback
from contextlib import contextmanager
from pathlib import Path
from typing import Dict, List, Optional

from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>, File, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware

# Load ingestion module if needed
from ingestion import ICMemoIngestion
from pydantic import BaseModel

load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="IC Memo Query Agent", version="1.0.0")

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database paths
IC_MEMOS_DB = "ic_memos.db"
USER_DATA_DB = "user_data.db"


# Request/Response models
class QueryRequest(BaseModel):
    question: str
    model: Optional[str] = "gpt-4o-mini"
    conversation_history: Optional[List[Dict]] = []


class QueryResponse(BaseModel):
    sql: str
    results: List[Dict]
    answer: Optional[str] = None
    error: Optional[str] = None


class SchemaResponse(BaseModel):
    tables: Dict[str, List[Dict]]
    sample_queries: List[str]


@contextmanager
def get_db_connection(db_path: str):
    """Context manager for database connections"""
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()


@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    # Check if IC memos database exists
    if not Path(IC_MEMOS_DB).exists():
        print("IC Memos database not found. Ingesting memos...")
        ingestion = ICMemoIngestion(IC_MEMOS_DB)

        # Check for CSV file first
        csv_path = "/Users/<USER>/Code3b/ic_memo_nl_sql/access_markdown/csv/_init_IC_MEMOS.csv"
        if Path(csv_path).exists():
            # Quick ingest from CSV
            ingestion.ingest_from_csv(csv_path)
        else:
            # Full ingestion from markdown files
            ingestion.ingest_all_memos()

    print(f"✅ Server started. IC Memos DB: {IC_MEMOS_DB}")


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"status": "healthy", "service": "IC Memo Query Agent"}


@app.get("/api/health")
async def health_check():
    """Detailed health check"""
    with get_db_connection(IC_MEMOS_DB) as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM memos")
        memo_count = cursor.fetchone()["count"]

        cursor.execute("SELECT COUNT(*) as count FROM deals")
        deal_count = cursor.fetchone()["count"]

    return {"status": "healthy", "memos_loaded": memo_count, "deals_tracked": deal_count}


@app.get("/api/schema", response_model=SchemaResponse)
async def get_schema():
    """Get database schema and sample queries"""
    with get_db_connection(IC_MEMOS_DB) as conn:
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        schema = {}
        for table in tables:
            table_name = table["name"]
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            schema[table_name] = [{"name": col["name"], "type": col["type"]} for col in columns]

    sample_queries = [
        "Show me all deals in the pest control industry",
        "Which companies have EBITDA over $10 million?",
        "What's the status of the car dealerships platform?",
        "List all deals marked as 'in_process'",
        "Show me the latest investment committee updates",
        "Which deals have LOIs submitted?",
        "What are the key priorities for Precision Pest Partners?",
    ]

    return SchemaResponse(tables=schema, sample_queries=sample_queries)


@app.post("/api/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process natural language query and return SQL results"""
    try:
        # Import query generator
        from core.query_generator import generate_sql_from_nl

        # Generate SQL from natural language
        sql = generate_sql_from_nl(request.question, db_path=IC_MEMOS_DB, model=request.model, history=request.conversation_history)

        # Execute query safely
        with get_db_connection(IC_MEMOS_DB) as conn:
            cursor = conn.cursor()
            cursor.execute(sql)
            rows = cursor.fetchall()

            # Convert to list of dicts
            results = [dict(row) for row in rows]

        # Generate natural language answer if results exist
        answer = None
        if results:
            answer = f"Found {len(results)} results for your query."

        return QueryResponse(sql=sql, results=results, answer=answer)

    except Exception as e:
        traceback.print_exc()
        return QueryResponse(sql="", results=[], error=str(e))


@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload CSV or JSON file to user database"""
    if not file.filename.endswith((".csv", ".json")):
        raise HTTPException(400, "Only CSV and JSON files are supported")

    # Process uploaded file
    content = await file.read()
    table_name = Path(file.filename).stem.replace(" ", "_").lower()

    try:
        with get_db_connection(USER_DATA_DB) as conn:
            cursor = conn.cursor()

            if file.filename.endswith(".csv"):
                import csv
                import io

                # Parse CSV
                csv_reader = csv.DictReader(io.StringIO(content.decode("utf-8")))
                rows = list(csv_reader)

                if rows:
                    # Create table based on first row
                    columns = list(rows[0].keys())
                    col_defs = ", ".join([f'"{col}" TEXT' for col in columns])
                    cursor.execute(f'CREATE TABLE IF NOT EXISTS "{table_name}" ({col_defs})')

                    # Insert data
                    placeholders = ", ".join(["?" for _ in columns])
                    for row in rows:
                        values = [row.get(col, "") for col in columns]
                        cursor.execute(f'INSERT INTO "{table_name}" VALUES ({placeholders})', values)

            conn.commit()

        return {"message": f"Successfully uploaded {file.filename}", "table": table_name, "rows": len(rows) if "rows" in locals() else 0}

    except Exception as e:
        raise HTTPException(500, f"Error processing file: {str(e)}")


@app.post("/api/ingest")
async def ingest_memos():
    """Manually trigger memo ingestion"""
    try:
        ingestion = ICMemoIngestion(IC_MEMOS_DB)
        ingestion.ingest_all_memos()
        return {"message": "Ingestion completed successfully"}
    except Exception as e:
        raise HTTPException(500, f"Ingestion failed: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
