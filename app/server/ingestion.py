"""
(Claude) IC Memo Ingestion Module
Parses Access IC memos and loads them into SQLite database for NL-to-SQL queries.

⛔ Out of scope: Manual parsing, regex extraction
⏳ TODO: Add batch processing, error recovery
☑️ Parse markdown memos using LLM with structured output
☑️ Create normalized database schema
☑️ Insert parsed data into SQLite
"""

import json
import os
import sqlite3
from pathlib import Path
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel

load_dotenv()


# Pydantic models for structured extraction
class Company(BaseModel):
    """Company mentioned in IC memo"""

    name: str
    industry: Optional[str] = None
    revenue: Optional[float] = None
    ebitda: Optional[float] = None
    location: Optional[str] = None
    description: Optional[str] = None


class Deal(BaseModel):
    """Deal/Investment opportunity"""

    name: str
    status: str  # e.g., "in_process", "evaluating", "closed"
    deal_type: Optional[str] = None  # platform, add-on, etc.
    companies: List[Company] = []
    priorities: List[str] = []
    initiatives: List[str] = []


class ICMemoData(BaseModel):
    """Structured data extracted from IC memo"""

    memo_date: str
    memo_type: str  # update, meeting, POV
    deals: List[Deal] = []
    key_metrics: Dict[str, Any] = {}


class ICMemoIngestion:
    """Handles ingestion of IC memos into database"""

    def __init__(self, db_path: str = "ic_memos.db"):
        self.db_path = db_path
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def create_schema(self):
        """Create database schema for IC memo data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Main tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS memos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT UNIQUE,
                memo_date DATE,
                memo_type TEXT,
                raw_content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS deals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                memo_id INTEGER,
                name TEXT,
                status TEXT,
                deal_type TEXT,
                FOREIGN KEY (memo_id) REFERENCES memos(id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS companies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                deal_id INTEGER,
                name TEXT,
                industry TEXT,
                revenue REAL,
                ebitda REAL,
                location TEXT,
                description TEXT,
                FOREIGN KEY (deal_id) REFERENCES deals(id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS priorities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                deal_id INTEGER,
                priority TEXT,
                FOREIGN KEY (deal_id) REFERENCES deals(id)
            )
        """)

        conn.commit()
        conn.close()

    def parse_memo_with_llm(self, content: str, filename: str) -> ICMemoData:
        """Use LLM to extract structured data from memo"""

        prompt = f"""
        Extract structured investment data from this IC memo.

        Memo content:
        {content[:8000]}  # Limit to avoid token limits

        Extract:
        1. Memo date (from filename or content)
        2. Type of memo (update, meeting, POV, etc.)
        3. All deals/investments mentioned with:
           - Deal name
           - Status (in_process, evaluating, LOI submitted, etc.)
           - Companies involved
           - Key priorities
           - Strategic initiatives
        4. Financial metrics (revenue, EBITDA, valuations)

        Return as JSON matching this structure:
        {{
            "memo_date": "YYYY-MM-DD",
            "memo_type": "update|meeting|POV",
            "deals": [
                {{
                    "name": "Deal Name",
                    "status": "status",
                    "companies": [
                        {{"name": "Company", "industry": "Industry", "revenue": 0, "ebitda": 0}}
                    ],
                    "priorities": ["priority1", "priority2"],
                    "initiatives": ["initiative1"]
                }}
            ]
        }}
        """

        response = self.client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "system", "content": "You extract structured investment data from IC memos."}, {"role": "user", "content": prompt}],
            response_format={"type": "json_object"},
            temperature=0,
        )

        parsed_data = json.loads(response.choices[0].message.content)
        return ICMemoData(**parsed_data)

    def ingest_memo(self, filepath: str):
        """Ingest a single IC memo file"""
        content = Path(filepath).read_text()
        filename = Path(filepath).name

        # Parse with LLM
        memo_data = self.parse_memo_with_llm(content, filename)

        # Insert into database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Insert memo
        cursor.execute(
            """
            INSERT OR REPLACE INTO memos (filename, memo_date, memo_type, raw_content)
            VALUES (?, ?, ?, ?)
        """,
            (filename, memo_data.memo_date, memo_data.memo_type, content),
        )

        memo_id = cursor.lastrowid

        # Insert deals and related data
        for deal in memo_data.deals:
            cursor.execute(
                """
                INSERT INTO deals (memo_id, name, status, deal_type)
                VALUES (?, ?, ?, ?)
            """,
                (memo_id, deal.name, deal.status, deal.deal_type),
            )

            deal_id = cursor.lastrowid

            # Insert companies
            for company in deal.companies:
                cursor.execute(
                    """
                    INSERT INTO companies (deal_id, name, industry, revenue, ebitda, location, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (deal_id, company.name, company.industry, company.revenue, company.ebitda, company.location, company.description),
                )

            # Insert priorities
            for priority in deal.priorities:
                cursor.execute(
                    """
                    INSERT INTO priorities (deal_id, priority)
                    VALUES (?, ?)
                """,
                    (deal_id, priority),
                )

        conn.commit()
        conn.close()

        print(f"✅ Ingested: {filename} - {len(memo_data.deals)} deals")

    def _insert_memo_data(self, filename: str, date: str, memo_data: ICMemoData, content: str):
        """Helper to insert memo data into database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Insert memo
        cursor.execute(
            """
            INSERT OR REPLACE INTO memos (filename, memo_date, memo_type, raw_content)
            VALUES (?, ?, ?, ?)
        """,
            (filename, date, memo_data.memo_type, content),
        )

        memo_id = cursor.lastrowid

        # Insert deals and related data
        for deal in memo_data.deals:
            cursor.execute(
                """
                INSERT INTO deals (memo_id, name, status, deal_type)
                VALUES (?, ?, ?, ?)
            """,
                (memo_id, deal.name, deal.status, deal.deal_type),
            )

            deal_id = cursor.lastrowid

            # Insert companies
            for company in deal.companies:
                cursor.execute(
                    """
                    INSERT INTO companies (deal_id, name, industry, revenue, ebitda, location, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (deal_id, company.name, company.industry, company.revenue, company.ebitda, company.location, company.description),
                )

            # Insert priorities
            for priority in deal.priorities:
                cursor.execute(
                    """
                    INSERT INTO priorities (deal_id, priority)
                    VALUES (?, ?)
                """,
                    (deal_id, priority),
                )

        conn.commit()
        conn.close()

    def ingest_from_csv(self, csv_path: str):
        """Ingest IC memos from CSV file (faster alternative)"""
        import csv

        self.create_schema()

        with open(csv_path, "r", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                try:
                    # Parse memo content with LLM
                    memo_data = self.parse_memo_with_llm(row["full_md_content"], row["filename"])

                    # Insert into database
                    self._insert_memo_data(
                        row["filename"], row["date"] if row["date"] != "Unknown Date" else memo_data.memo_date, memo_data, row["full_md_content"]
                    )

                    print(f"✅ Ingested: {row['filename']}")
                except Exception as e:
                    print(f"❌ Error ingesting {row['filename']}: {e}")

        print(f"\n✅ CSV ingestion complete! Database: {self.db_path}")

    def ingest_all_memos(self, memo_dir: str = "/Users/<USER>/Code3b/ic_memo_nl_sql/Access IC Memos"):
        """Ingest all IC memo files from directory"""
        self.create_schema()

        memo_files = list(Path(memo_dir).glob("*.md"))
        print(f"Found {len(memo_files)} memo files to ingest")

        for memo_file in memo_files:
            try:
                self.ingest_memo(str(memo_file))
            except Exception as e:
                print(f"❌ Error ingesting {memo_file.name}: {e}")

        print(f"\n✅ Ingestion complete! Database: {self.db_path}")


if __name__ == "__main__":
    # Use CSV for faster ingestion
    csv_path = "/Users/<USER>/Code3b/ic_memo_nl_sql/access_markdown/csv/_init_IC_MEMOS.csv"
    if Path(csv_path).exists():
        print("Using CSV file for faster ingestion...")
        ingestion = ICMemoIngestion()
        ingestion.ingest_from_csv(csv_path)
    else:
        print("CSV not found, using direct markdown ingestion...")
        ingestion = ICMemoIngestion()
        ingestion.ingest_all_memos()
