"""
(Claude) Natural Language to SQL Query Generator
Converts investment-related questions to SQL queries with domain context.
"""

import os
import sqlite3
from typing import Dict, List, Optional

from anthropic import Anthropic
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()


class InvestmentQueryGenerator:
    """Generates SQL queries from natural language with investment context"""

    def __init__(self, db_path: str, model: str = "gpt-4o-mini"):
        self.db_path = db_path
        self.model = model
        self.schema = self._get_schema()

        # Initialize clients
        if "claude" in model.lower() or "anthropic" in model.lower():
            self.client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        else:
            self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def _get_schema(self) -> str:
        """Get database schema as string"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get all tables and their schemas
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table'")
        schemas = cursor.fetchall()
        conn.close()

        return "\n".join([s[0] for s in schemas if s[0]])

    def _build_investment_context(self) -> str:
        """Build investment-specific context"""
        return """
        Investment terminology context:
        - EBITDA: Earnings Before Interest, Taxes, Depreciation, and Amortization
        - LOI: Letter of Intent
        - M&A: Mergers and Acquisitions
        - Platform: Initial acquisition in a roll-up strategy
        - Add-on/Infill: Subsequent acquisitions to a platform
        - IC: Investment Committee
        - POV: Point of View
        - WTO: "Want To Own" - target companies
        - ACE: Associates in Chief of Staff/Corp Dev roles

        Common query patterns:
        - Deal status: in_process, evaluating, LOI submitted, closed
        - Industries: pest control, car dealerships, portable storage, roofing
        - Metrics: revenue, EBITDA, valuation multiples
        - Priorities and initiatives are stored in separate tables linked by deal_id
        """

    def generate_sql(self, question: str, history: Optional[List[Dict]] = None) -> str:
        """Generate SQL from natural language question"""

        context = self._build_investment_context()

        # Build history context if provided
        history_context = ""
        if history:
            history_context = "Previous queries in this conversation:\n"
            for h in history[-3:]:  # Last 3 queries
                history_context += f"Q: {h.get('question', '')}\nSQL: {h.get('sql', '')}\n"

        prompt = f"""
        You are an expert at converting investment-related questions to SQL queries.

        Database schema:
        {self.schema}

        Investment context:
        {context}

        {history_context}

        Convert this question to a SQL query:
        "{question}"

        Rules:
        1. Return ONLY the SQL query, no explanation
        2. Use appropriate JOINs to get complete information
        3. Include relevant columns for comprehensive answers
        4. Use LIKE for partial text matches
        5. Order results meaningfully (by date, value, etc.)
        6. Limit results to 50 unless specified otherwise

        SQL Query:
        """

        if isinstance(self.client, Anthropic):
            response = self.client.messages.create(
                model="claude-3-5-sonnet-20241022", messages=[{"role": "user", "content": prompt}], max_tokens=500, temperature=0
            )
            sql = response.content[0].text.strip()
        else:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You convert natural language to SQL. Return only the SQL query."},
                    {"role": "user", "content": prompt},
                ],
                temperature=0,
                max_tokens=500,
            )
            sql = response.choices[0].message.content.strip()

        # Clean up the SQL
        sql = sql.replace("```sql", "").replace("```", "").strip()

        # Add LIMIT if not present
        if "LIMIT" not in sql.upper():
            sql += " LIMIT 50"

        return sql


def generate_sql_from_nl(question: str, db_path: str, model: str = "gpt-4o-mini", history: Optional[List[Dict]] = None) -> str:
    """Main function to generate SQL from natural language"""
    generator = InvestmentQueryGenerator(db_path, model)
    return generator.generate_sql(question, history)
