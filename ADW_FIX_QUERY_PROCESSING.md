# ADW Workflow Prompt: Fix Natural Language Query Processing

## Critical Issue
The IC Memo Query Agent was built but **failed to process queries correctly**. The natural language to SQL conversion is broken, preventing users from querying the investment committee data.

## Current State
- ✅ Database successfully populated with 12 memos, 39 deals, 37 companies, 59 priorities
- ✅ FastAPI server running with endpoints
- ✅ Web interface accessible
- ❌ **Natural language queries fail to execute**
- ❌ **SQL generation produces errors or incorrect results**

## Primary Objective
**Fix the query processing system so users can ask questions in natural language and get accurate SQL results from the IC memo data.**

## Specific Problems to Fix

### 1. Query Generation Issues
The `/api/query` endpoint in `server.py` is not correctly:
- Converting natural language to valid SQL
- Handling investment-specific terminology
- Managing table joins for complex queries
- Preserving context across conversation turns

### 2. Test These Failing Queries
Ensure these queries work correctly:
- "Show me all deals in pest control"
- "Which companies have EBITDA over 10 million?"
- "What are the key priorities for deals in process?"
- "List all car dealership platforms"
- "Show financial metrics for closed deals"

### 3. SQL Generation Problems
Current issues in `query_generator.py`:
- Invalid SQL syntax being generated
- Incorrect table/column references
- Missing JOIN clauses for multi-table queries
- Poor handling of financial metric formats
- No proper error recovery

## Required Fixes

### 1. Enhanced Query Understanding
- Improve prompt to better understand investment terminology
- Add examples of IC-specific queries to the system prompt
- Map common terms to database columns (e.g., "in process" → status='in_process')

### 2. SQL Generation Improvements
```python
# Fix the generate_sql_query function to:
- Validate generated SQL before execution
- Add proper JOIN logic for multi-table queries
- Handle financial metrics with correct operators
- Escape special characters properly
- Add query optimization for performance
```

### 3. Error Handling & Recovery
- Catch SQL execution errors gracefully
- Attempt query reformulation on failure
- Provide helpful error messages to users
- Log failed queries for debugging

### 4. Context Management
- Properly maintain conversation history
- Use context for follow-up queries
- Handle pronouns and references to previous results

## Test Coverage Required
Create and run tests for:
1. Basic single-table queries
2. Multi-table JOIN queries
3. Aggregation queries (COUNT, SUM, AVG)
4. Financial metric queries with operators
5. Status-based filtering
6. Industry-specific searches
7. Date-based queries
8. Context-aware follow-up queries

## Success Criteria
- [ ] All test queries execute successfully
- [ ] 95% query success rate on first attempt
- [ ] Clear error messages for failed queries
- [ ] Sub-second response time for most queries
- [ ] Proper handling of ambiguous queries
- [ ] Context preservation across conversation

## Implementation Steps

### Step 1: Debug Current Failures
- Add comprehensive logging to query generation
- Identify exact failure points in the pipeline
- Document specific SQL errors being produced

### Step 2: Fix SQL Generation
- Improve the LLM prompt for SQL generation
- Add validation layer before execution
- Implement query rewriting for common patterns

### Step 3: Test Thoroughly
- Run all test queries
- Verify results against known data
- Test edge cases and error conditions

### Step 4: Enhance User Experience
- Add query suggestions based on available data
- Improve error messages
- Add query history tracking

## Files to Modify
1. `app/server/query_generator.py` - Core SQL generation logic
2. `app/server/server.py` - API endpoint handling
3. `app/server/core/config.py` - Configuration and prompts
4. Add: `app/server/tests/test_queries.py` - Comprehensive test suite

## Do NOT Change
- The database schema (it's working correctly)
- The ingestion system (data is loaded properly)
- The frontend interface (it's functional)

## Key Insight
The data is successfully in the database. The markdown processing works. The web interface is ready. **The ONLY problem is the natural language to SQL conversion is failing.** Fix this one critical component and the entire system will work.

## Validation
After fixes, verify these metrics:
- Query success rate: >95%
- Response time: <1 second
- Financial accuracy: 100%
- No SQL injection vulnerabilities
- Proper error handling for edge cases
