# (<PERSON>) Justfile for IC Memo Query Agent - Quick commands for development
# A minimalist cheatsheet: just <command> runs tasks, just --list shows all

# Default task - show available commands
default:
    @just --list

# Run the full application (server + client)
run:
    ./run.sh

# Start only the API server
server:
    cd app/server && source .venv/bin/activate && uvicorn server:app --reload

# Open client in browser
client:
    open app/client/index.html

# Run ingestion to update database
ingest:
    cd app/server && source .venv/bin/activate && python ingestion.py

# Install Python dependencies
install:
    cd app/server && python3 -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt

# Clean database and re-ingest
clean:
    rm -f app/server/ic_memos.db app/server/user_data.db
    just ingest

# Test with sample queries
test:
    @echo "Testing API endpoints..."
    @curl -s http://localhost:8000/api/health | python3 -m json.tool
    @echo "\n✅ Testing investment queries..."
    @curl -s -X POST http://localhost:8000/api/query \
        -H "Content-Type: application/json" \
        -d '{"question": "Show me all deals in pest control"}' \
        | python3 -m json.tool

# Show API documentation
docs:
    open http://localhost:8000/docs

# Show database schema
schema:
    @echo "Database schema:"
    @sqlite3 app/server/ic_memos.db ".schema"

# Interactive database query
query:
    sqlite3 app/server/ic_memos.db
