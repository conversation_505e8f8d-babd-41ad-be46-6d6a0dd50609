# Issue #001: Complete PDF Extraction and Validation Pipeline for IC Memos

## Problem Statement
The current implementation processes markdown files, but the actual requirement is to extract, parse, and validate data from PDF files containing investment committee memos. The system must provide high confidence in data extraction accuracy.

## Requirements

### Core Requirements
1. **PDF Data Extraction**
   - Extract ALL text, tables, and structured data from IC memo PDFs
   - Handle multi-page documents with complex layouts
   - Preserve document structure and relationships
   - Extract financial metrics (EBITDA, revenue, valuations)
   - Capture deal names, companies, priorities, and initiatives
   - Maintain date and document metadata

2. **Multi-Library Comparison**
   - Implement extraction using at least 3 different PDF libraries:
     - PyPDF2 for basic text extraction
     - pdfplumber for table and structured data
     - pdf2image + OCR (Tesseract) for scanned documents
     - Consider: PDFMiner, Camelot, Tabula-py
   - Compare extraction results across libraries
   - Create ensemble approach for maximum accuracy

3. **Double-Blind Validation System**
   - Use two separate git worktrees for independent implementations
   - Worktree A: Primary extraction pipeline
   - Worktree B: Validation pipeline with different approach
   - Compare results between worktrees
   - Flag discrepancies for manual review
   - Calculate confidence scores for each extraction

4. **Parallel Processing Architecture**
   - Use multiple sub-agents for different extraction tasks:
     - Text extraction agent
     - Table extraction agent
     - Financial metrics agent
     - Entity recognition agent
     - Data cleaning agent
   - Orchestrate agents to work in parallel
   - Merge and reconcile results

5. **Data Standardization Pipeline**
   - Normalize extracted data into consistent schema
   - Handle variations in document formats
   - Clean and validate financial numbers
   - Standardize company names and entities
   - Create unified date formats

6. **Validation Criteria**
   - **Completeness**: All sections of PDFs must be extracted
   - **Accuracy**: Financial figures must match exactly
   - **Consistency**: Same data extracted by multiple methods must match
   - **Coverage**: No data left behind in PDFs
   - **Confidence**: Each extraction must have confidence score >95%

## Technical Implementation Plan

### Phase 1: PDF Discovery and Analysis
```python
# Find all PDF files
# Analyze PDF structure (text-based vs scanned)
# Categorize by complexity
# Create test dataset with known values
```

### Phase 2: Multi-Library Implementation
```python
# Implement extraction with each library:
extractors = {
    'pypdf2': PyPDF2Extractor(),
    'pdfplumber': PdfPlumberExtractor(),
    'ocr': OCRExtractor(),
    'pdfminer': PDFMinerExtractor(),
}

# Run all extractors in parallel
results = parallel_extract(pdf_file, extractors)

# Compare and merge results
merged = ensemble_merge(results, confidence_threshold=0.95)
```

### Phase 3: Double-Blind Validation
```bash
# Worktree A: Primary implementation
git worktree add trees/extraction_primary
cd trees/extraction_primary
# Implement primary extraction pipeline

# Worktree B: Validation implementation
git worktree add trees/extraction_validation
cd trees/extraction_validation
# Implement independent validation pipeline

# Compare results
python validate_extraction.py --primary trees/extraction_primary/output \
                             --validation trees/extraction_validation/output
```

### Phase 4: Parallel Agent System
```python
agents = [
    TextExtractionAgent(),
    TableExtractionAgent(),
    FinancialMetricsAgent(),
    EntityRecognitionAgent(),
    DataCleaningAgent()
]

# Run agents in parallel
with ThreadPoolExecutor(max_workers=5) as executor:
    futures = [executor.submit(agent.process, pdf) for agent in agents]
    results = [f.result() for f in futures]

# Merge agent outputs
final_data = merge_agent_results(results)
```

### Phase 5: Validation and Testing
```python
# Success criteria
validation_tests = [
    test_all_pages_extracted,
    test_financial_accuracy,
    test_entity_extraction,
    test_date_parsing,
    test_table_extraction,
    test_cross_library_consistency,
    test_confidence_scores
]

# Run validation suite
for test in validation_tests:
    assert test(extracted_data), f"{test.__name__} failed"
```

## Expected Outputs

1. **Structured Database**
   - SQLite with validated, clean data
   - Tables: pdfs, pages, sections, deals, companies, financials
   - Full text search capability
   - Confidence scores for each field

2. **Validation Report**
   - Extraction accuracy metrics
   - Library comparison results
   - Discrepancy analysis
   - Confidence distribution

3. **Query Interface**
   - Natural language to SQL for PDF-extracted data
   - Confidence-aware query responses
   - Source document references

## Success Criteria

- [ ] Extract 100% of text from all PDFs
- [ ] Achieve >98% accuracy on financial metrics
- [ ] Successfully extract all tables with structure preserved
- [ ] Pass double-blind validation with <2% discrepancy
- [ ] All extracted data queryable via NL interface
- [ ] Confidence score >95% for all critical fields
- [ ] Complete extraction of 50+ PDF documents
- [ ] Parallel processing reduces extraction time by >60%
- [ ] Validation suite passes with zero critical errors

## Error Handling

- Implement retry logic for failed extractions
- Fallback to OCR for corrupted PDFs
- Manual review queue for low-confidence extractions
- Detailed logging of all extraction attempts
- Recovery mechanism for partial failures

## Performance Requirements

- Process 10 PDFs per minute minimum
- Sub-second query response time
- Memory usage <2GB for pipeline
- Support PDFs up to 500 pages

## Documentation Requirements

- Complete API documentation
- Extraction methodology document
- Validation process flowchart
- Troubleshooting guide
- Performance benchmarks

---

## Implementation Command

To implement this solution using the ADW workflow:

```bash
cd /Users/<USER>/Code3b/ic_memo_nl_sql/adws
uv run adw_plan_build_test_review_iso.py 001
```

This will trigger the isolated workflow to:
1. Plan the complete PDF extraction solution
2. Build the multi-library implementation
3. Test with comprehensive validation
4. Review against all success criteria

## Priority: CRITICAL
This is the core requirement that was missed in the initial implementation. The entire system depends on accurate PDF extraction.
