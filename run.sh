#!/bin/bash
# (<PERSON>) Run script for IC Memo Query Agent
# Starts the FastAPI server and opens the client interface

echo "🚀 Starting IC Memo Query Agent..."

# Navigate to server directory
cd app/server

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment and install dependencies
source .venv/bin/activate
echo "Installing dependencies..."
pip install -q -r requirements.txt 2>/dev/null || pip install -q python-dotenv openai anthropic pydantic fastapi uvicorn

# Check if database exists
if [ ! -f "ic_memos.db" ]; then
    echo "Database not found. Running ingestion..."
    python ingestion.py
fi

# Start the server in background
echo "Starting API server on http://localhost:8000..."
uvicorn server:app --reload --host 0.0.0.0 --port 8000 &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 3

# Open the client in browser
echo "Opening client interface..."
open ../../app/client/index.html

echo "✅ IC Memo Query Agent is running!"
echo "   - API Server: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo "   - Client: file://$(pwd)/../../app/client/index.html"
echo ""
echo "Press Ctrl+C to stop the server"

# Wait for user to stop
trap "kill $SERVER_PID 2>/dev/null; echo 'Server stopped.'; exit" INT
wait $SERVER_PID
