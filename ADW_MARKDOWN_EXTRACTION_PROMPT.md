# ADW Workflow Prompt: Adapt Multi-Source Extraction System for Markdown IC Memos

## Context & Problem Discovery
We initially built a PDF extraction pipeline, but discovered the Access IC Demo Corpus consists of 48 markdown files (`.md`), not PDFs. These markdown files contain the actual investment committee memos with complex financial data, tables, and structured information that needs extraction.

## Critical Requirement
**Adapt the 10-solution extraction approach to work with markdown files while maintaining the same validation rigor and confidence scoring originally intended for PDFs.**

## Core Objectives

### 1. Multi-Parser Markdown Extraction
Implement parallel extraction using multiple markdown parsing approaches:
- **CommonMark Parser**: Standard markdown parsing with strict compliance
- **Python-Markdown**: Extended markdown with table support
- **Mistune**: High-performance markdown parser
- **Markdown-it-py**: Port of markdown-it with plugin support
- **Marko**: Pure Python markdown parser with AST
- **LLM-based extraction**: GPT-4/Claude for semantic understanding
- **Regex-based extraction**: Pattern matching for financial metrics
- **Beautiful Soup**: HTML intermediate parsing
- **Pandoc**: Universal document converter approach
- **Custom IC-specific parser**: Tailored to IC memo structure

### 2. Double-Blind Validation (Keep Existing Approach)
- Worktree A: Primary extraction using parsers 1-5
- Worktree B: Validation using parsers 6-10
- Compare results between worktrees
- Calculate confidence scores for each field
- Flag discrepancies >2% for review

### 3. Data Extraction Requirements
Extract from the 48 markdown files in `/Access IC Memos/`:
- **Deal Information**: Names, stages (LOI, in process, closed, etc.)
- **Company Data**: Names, industries, financial metrics
- **Financial Metrics**: EBITDA, revenue, valuations (with 100% accuracy)
- **Priorities & Initiatives**: Key action items per deal
- **Dates & Timelines**: Meeting dates, deadlines, milestones
- **Investment Thesis**: Strategic rationale and synergies
- **Risk Factors**: Identified concerns and mitigation strategies

### 4. Specific Markdown Challenges to Handle
- **Tables**: Various markdown table formats (pipe tables, HTML tables)
- **Nested Lists**: Multi-level priority and initiative lists
- **Financial Formats**: $XXM, $XX.XM, XX%, X.Xx formats
- **Headers**: Inconsistent header levels across memos
- **Special Characters**: M&A notation, industry-specific terms
- **Cross-references**: Links between related deals/companies

### 5. Parallel Processing Architecture
```
markdown_extraction/
├── extractors/
│   ├── commonmark_extractor.py
│   ├── python_markdown_extractor.py
│   ├── mistune_extractor.py
│   ├── markdown_it_extractor.py
│   ├── marko_extractor.py
│   ├── llm_extractor.py
│   ├── regex_extractor.py
│   ├── beautifulsoup_extractor.py
│   ├── pandoc_extractor.py
│   └── ic_custom_extractor.py
├── validators/
│   ├── double_blind.py
│   ├── confidence_scorer.py
│   └── discrepancy_analyzer.py
├── pipeline/
│   ├── orchestrator.py
│   ├── parallel_processor.py
│   └── data_standardizer.py
└── tests/
    ├── test_extraction_accuracy.py
    └── golden_dataset/
        └── verified_extractions.json
```

### 6. Validation Strategy
- **Ground Truth**: Manually verify 3 sample memos for 100% accurate baseline
- **Cross-Parser Agreement**: Require 80% agreement across parsers
- **Financial Accuracy**: Zero tolerance for financial metric errors
- **Structure Preservation**: Maintain hierarchical relationships
- **Completeness Check**: Ensure no data loss during extraction

### 7. Success Criteria
- [ ] All 48 markdown files successfully processed
- [ ] 100% accuracy on financial metrics (EBITDA, revenue, valuations)
- [ ] >95% confidence score on all extracted fields
- [ ] All 10 parsing methods implemented and compared
- [ ] Double-blind validation shows <2% discrepancy
- [ ] Processing time <5 seconds per markdown file
- [ ] Unified schema output compatible with SQL database
- [ ] Natural language query interface functioning on extracted data

### 8. Implementation Approach

#### Phase 1: Parser Implementation (Parallel)
- Launch 10 sub-agents, one for each parser
- Each implements extraction for the same test file
- Standardize output format across all parsers

#### Phase 2: Validation Framework
- Implement ensemble voting system
- Create confidence scoring algorithm
- Build discrepancy detection and reporting

#### Phase 3: Full Pipeline
- Process all 48 markdown files
- Generate validation report
- Load into SQLite database
- Test with natural language queries

### 9. Special Considerations
- **Preserve existing markdown processing**: The current `ingestion.py` works but needs validation
- **Reuse SQL infrastructure**: Keep existing FastAPI server and query system
- **Incremental updates**: Support adding new memos without full reprocessing
- **Audit trail**: Log all extraction decisions and confidence scores

### 10. Deliverables
1. **10 functioning markdown extractors** with standardized output
2. **Validation system** with confidence scoring and discrepancy detection
3. **Complete extracted dataset** from all 48 IC memos
4. **Performance benchmarks** comparing all extraction methods
5. **Integration** with existing NL-to-SQL query system
6. **Test suite** with >90% coverage
7. **Documentation** of extraction methodology and accuracy metrics

## Key Insight
While the original requirement mentioned PDFs, the actual data is in markdown format. This is actually advantageous because:
- Markdown is structured text (easier to parse than PDFs)
- No OCR or layout detection needed
- Tables are already in parseable format
- Can achieve higher accuracy with simpler methods

**However**, we should still maintain the multi-parser validation approach to ensure maximum accuracy and confidence in the extracted data, especially for critical financial metrics.

## Final Note
The system should be designed to easily adapt if PDF files become available in the future. The validation framework and confidence scoring system will work regardless of input format.
