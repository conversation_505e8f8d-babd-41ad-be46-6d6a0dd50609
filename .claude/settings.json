{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Bash(./scripts/copy_dot_env.sh:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)"], "deny": ["Bash(git push --force:*)", "Bash(git push -f:*)", "Bash(rm -rf:*)"]}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/pre_tool_use.py || true"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/post_tool_use.py || true"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/notification.py --notify || true"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/stop.py --chat || true"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/subagent_stop.py || true"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/pre_compact.py || true"}]}], "UserPromptSubmit": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run $CLAUDE_PROJECT_DIR/.claude/hooks/user_prompt_submit.py --log-only || true"}]}]}}