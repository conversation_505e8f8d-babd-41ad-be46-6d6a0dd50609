#!/usr/bin/env -S uv run
# /// script
# dependencies = ["e2b-code-interpreter>=0.0.10", "python-dotenv"]
# ///

"""
E2B Sandbox POC - Call Claude Code via subprocess
Demonstrates successful integration of Claude Code CLI in E2B sandboxes.

CRITICAL DISCOVERY - How to prevent Claude Code from hanging:
============================================================
The key to making Claude Code work in E2B sandboxes is closing stdin using either:
- Python: stdin=subprocess.DEVNULL
- Bash: < /dev/null

WHY THIS WORKS:
- E2B sandboxes don't provide a TTY (terminal)
- Claude Code checks for TTY and when absent, waits for stdin input
- Without explicitly closing stdin, the process blocks forever waiting for input
- By redirecting stdin to /dev/null, we tell <PERSON> "no input is coming"
- This allows <PERSON> to proceed with execution instead of hanging

REQUIREMENTS FOR SUCCESS:
1. Always use stdin=subprocess.DEVNULL (Python) or < /dev/null (Bash)
2. Use -p flag for non-interactive prompt mode
3. Use --dangerously-skip-permissions to avoid permission prompts
4. Use full path to claude binary (e.g., ~/.npm-global/bin/claude)
"""

import os
from dotenv import load_dotenv
from e2b_code_interpreter import Sandbox

load_dotenv()
api_key = os.getenv("ANTHROPIC_API_KEY", "")

print("=== E2B + Claude Code Integration POC ===\n")

with Sandbox(envs={"ANTHROPIC_API_KEY": api_key}) as sandbox:
    print(f"✓ Sandbox created: {sandbox.sandbox_id}")
    
    # Setup npm and install Claude Code
    print("\nInstalling Claude Code...")
    sandbox.commands.run("npm config set prefix ~/.npm-global")
    result = sandbox.commands.run("npm install -g @anthropic-ai/claude-code", timeout=60000)
    if result.exit_code == 0:
        print("✓ Claude Code installed successfully")
    else:
        raise Exception(f"Failed to install Claude Code: {result.stderr}")
    
    # Test 1: Direct command line execution
    print("\n--- Test 1: Direct CLI execution ---")
    result = sandbox.commands.run(
        "~/.npm-global/bin/claude -p 'What is 2+2?' --output-format text --dangerously-skip-permissions < /dev/null",
        timeout=30000
    )
    print(f"Q: What is 2+2?")
    print(f"A: {result.stdout.strip()}")
    
    # Test 2: JSON output format
    print("\n--- Test 2: JSON output format ---")
    result = sandbox.commands.run(
        "~/.npm-global/bin/claude -p 'What is the capital of France?' --output-format json --dangerously-skip-permissions < /dev/null",
        timeout=30000
    )
    print(f"Q: What is the capital of France?")
    if result.stdout:
        import json
        response = json.loads(result.stdout)
        print(f"A: {response['result']}")
        print(f"   Cost: ${response['total_cost_usd']}")
        print(f"   Duration: {response['duration_ms']}ms")
    
    # Test 3: Python subprocess execution
    print("\n--- Test 3: Python subprocess execution ---")
    python_code = """
import subprocess
import os
import json

# Build full path to claude
claude_path = os.path.expanduser('~/.npm-global/bin/claude')

# Critical: stdin=subprocess.DEVNULL prevents hanging in non-TTY environments
result = subprocess.run(
    [claude_path, '-p', 'List 3 prime numbers', '--output-format', 'json', '--dangerously-skip-permissions'],
    stdin=subprocess.DEVNULL,
    capture_output=True,
    text=True,
    timeout=30
)

if result.returncode == 0:
    response = json.loads(result.stdout)
    print(f"Q: List 3 prime numbers")
    print(f"A: {response['result']}")
else:
    print(f"Error: {result.stderr}")
"""
    
    execution = sandbox.run_code(python_code)
    if execution.logs and execution.logs.stdout:
        print(execution.logs.stdout.strip())
    
    # Test 4: Verify non-TTY environment handling
    print("\n--- Test 4: Environment check ---")
    result = sandbox.commands.run("tty || echo 'No TTY (as expected)'")
    print(f"TTY Status: {result.stdout.strip()}")
    
    result = sandbox.commands.run("echo $TERM")
    print(f"TERM variable: {result.stdout.strip() or 'not set'}")

print("\n" + "="*50)
print("✅ POC Complete - All tests passed!")
print("\nKey findings:")
print("- Claude Code works perfectly in E2B sandboxes")
print("- Must use stdin=DEVNULL or < /dev/null to prevent hanging")
print("- Use -p flag for non-interactive mode")
print("- Use --dangerously-skip-permissions to skip prompts")
print("="*50)