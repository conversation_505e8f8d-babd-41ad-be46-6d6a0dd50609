"""
(Claude) Data Schema for IC Memo Extraction

Defines the structured data model we're extracting from markdown memos.
"""

from typing import List, Optional, Dict
from pydantic import BaseModel, Field
from datetime import date


class FinancialMetric(BaseModel):
    """Financial metrics extracted from memos."""
    metric_type: str = Field(description="Type: EBITDA, revenue, valuation, etc.")
    value: float = Field(description="Numeric value")
    currency: str = Field(default="USD")
    period: Optional[str] = Field(default=None, description="Time period if specified")


class Priority(BaseModel):
    """Key priority or initiative."""
    description: str
    category: Optional[str] = None  # e.g., "Human Capital", "M&A"


class Deal(BaseModel):
    """Deal or investment opportunity."""
    deal_name: str = Field(description="Name of the deal/company")
    section: str = Field(description="Section in memo: Deals in Process, Portfolio, etc.")
    priorities: List[Priority] = Field(default_factory=list)
    financials: List[FinancialMetric] = Field(default_factory=list)
    notes: List[str] = Field(default_factory=list)


class ICMemo(BaseModel):
    """Complete IC Memo structured data."""
    memo_date: date
    source_file: str
    deals: List[Deal] = Field(default_factory=list)
    all_financials: List[FinancialMetric] = Field(default_factory=list)
    all_priorities: List[Priority] = Field(default_factory=list)
    raw_sections: Dict[str, str] = Field(default_factory=dict)
    extraction_method: str
    confidence_score: float = Field(ge=0.0, le=1.0)


# Target extraction fields
EXTRACTION_TARGETS = {
    "deals": "All deal/company names mentioned",
    "financials": "EBITDA, revenue, valuations, ARR, etc.",
    "priorities": "Key priorities over next 6 months",
    "initiatives": "Strategic initiatives",
    "sections": "Section 1: Deals in Process, Portfolio Updates, etc."
}
