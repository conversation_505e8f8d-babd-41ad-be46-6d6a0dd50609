# Isolated PDF Extraction Experiment (Claude)

**Mission**: Implement and benchmark 10 state-of-the-art PDF extraction solutions

**Status**: 🚧 In Progress

**Agent**: B0 (Primary Orchestrator)

---

## Quick Start

```bash
# From /isolated directory
cd isolated/

# Set up environment
uv venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
uv sync --all-extras

# Generate test PDFs
python md_to_pdf.py

# Run LlamaParse baseline
cd llamaparse/
python extract.py

# Run all extractors in parallel (via ADW)
../run_all.sh
```

---

## Directory Structure

```
isolated/
├── llamaparse/          # Solution 1: LlamaParse
├── docling/             # Solution 2: Docling
├── unstructured/        # Solution 3: Unstructured.io
├── pymupdf4llm/         # Solution 4: PyMuPDF4LLM
├── marker/              # Solution 5: Marker
├── mineru/              # Solution 6: MinerU
├── pypdf2/              # Solution 7: PyPDF2
├── pdfplumber/          # Solution 8: pdfplumber
├── camelot/             # Solution 9: Camelot
├── gemini_multimodal/   # Solution 10: Gemini 2.5 Pro
├── outputs/
│   ├── test_pdfs/       # Generated test PDFs
│   ├── llamaparse/      # Extraction results per solution
│   ├── docling/
│   └── ...
├── journal-agentB0.md   # Primary orchestrator journal
├── journal-agentB1.md   # Sub-agent journals
├── REQUIREMENTS_EARS.md # Formal requirements
├── pyproject.toml       # Dependencies
└── README.md            # This file
```

---

## 10 Solutions Overview

| # | Solution | Type | Speed | Accuracy | Key Feature |
|---|----------|------|-------|----------|-------------|
| 1 | LlamaParse | Commercial | ~6s | 9/10 | Structure preservation |
| 2 | Docling | Open Source | Medium | 97.9% | Table accuracy |
| 3 | Unstructured | Open Source | 1.29s | Good | RAG-optimized |
| 4 | PyMuPDF4LLM | Open Source | 0.12s | Excellent | Speed champion |
| 5 | Marker | Open Source | 11.3s | Perfect | Layout perfect |
| 6 | MinerU | Open Source | Medium | Good | LLM-ready |
| 7 | PyPDF2 | Open Source | Fast | Basic | Lightweight |
| 8 | pdfplumber | Open Source | Medium | Good | Table specialist |
| 9 | Camelot | Open Source | Medium | 73% | Table-focused |
| 10 | Gemini 2.5 Pro | Commercial | Fast | Best | Multimodal LLM |

---

## Current Status (2025-11-04)

### Completed ✓
- [x] Research 10 SotA solutions
- [x] Document EARS requirements (66 formal requirements)
- [x] Create directory structure
- [x] Set up Python project with uv
- [x] Create MD→PDF converter utility
- [x] Start orchestrator journal

### In Progress 🚧
- [ ] Generate test PDF corpus
- [ ] Implement LlamaParse baseline
- [ ] Launch sub-agents for parallel implementation

### Pending ⏳
- [ ] Implement remaining 9 solutions
- [ ] Create validation framework
- [ ] Run parallel extraction
- [ ] Generate comparison report

---

## Key Findings

### PDF Mystery Solved
The `../Access IC Memos/` directory contains 48 `.md` files that were already extracted from PDFs.

**Strategy**: Generate synthetic test PDFs from MD files, then:
1. Extract using all 10 solutions
2. Validate against ground-truth MD files
3. Calculate accuracy metrics
4. Ready for real PDFs when available

---

## API Keys Required

Create `.env` file in `/isolated/` with:

```bash
# Commercial solutions
LLAMA_CLOUD_API_KEY=llx-...
GOOGLE_API_KEY=...

# Optional
ANTHROPIC_API_KEY=...
OPENAI_API_KEY=...
```

Open-source solutions (no keys needed):
- Docling, Unstructured, PyMuPDF4LLM, Marker, MinerU, PyPDF2, pdfplumber, Camelot

---

## Success Criteria

| Metric | Threshold |
|--------|-----------|
| Text Accuracy | >98% |
| Table Accuracy | >95% |
| Financial Accuracy | 100% |
| All Solutions Implemented | 10/10 |
| Processing Speed | <1 min/PDF |

---

## Validation Approach

1. **Syntactic**: Valid markdown/JSON output
2. **Semantic**: Financial metrics + tables extracted
3. **Cross-Solution**: <5% variance between solutions
4. **Ground Truth**: Exact match against source MD files

---

## Journal Entries

- [journal-agentB0.md](journal-agentB0.md) - Primary orchestrator
- journal-agentB1.md - LlamaParse implementation (pending)
- journal-agentB2.md - Docling implementation (pending)
- ... (B3-B10 for remaining solutions)

---

## Notes

- All work confined to `/isolated/` directory (Requirement R.1.8)
- Using ADW workflows for parallel sub-agent execution
- Purposeful brittleness: explicit errors over silent failures
- Rich tables with dividers for readability

---

**Last Updated**: 2025-11-04T03:45Z
**Next Milestone**: Generate test PDFs + implement LlamaParse baseline
