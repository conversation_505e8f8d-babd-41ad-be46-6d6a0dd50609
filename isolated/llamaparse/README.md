# Solution 1: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>)

**Agent**: B1
**Type**: Commercial (Premium)
**Rating**: 9/10
**Key Feature**: Excellent structure preservation

## Installation

```bash
cd llamaparse/
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## API Key Setup

```bash
export LLAMA_CLOUD_API_KEY="llx-your-key-here"
# Or create .env file with:
# LLAMA_CLOUD_API_KEY=llx-your-key-here
```

Get API key from: https://cloud.llamaindex.ai

## Usage

```bash
python extract.py
```

## Strengths
- 9/10 rating in benchmarks
- Fast processing (~6 seconds)
- Excellent structure preservation
- Good for multi-column layouts
- Handles complex PDFs well

## Weaknesses
- Commercial (requires API key)
- Can struggle with detailed financials
- Occasional text casing alterations
- Currency symbol accuracy issues

## Benchmarks
- Speed: ~6s per PDF
- Accuracy: 90%
- Tables: Good
- Cost: Pay-per-use

## Output
- JSON: `../outputs/llamaparse/{filename}_llamaparse.json`
- Markdown: `../outputs/llamaparse/{filename}_llamaparse.md`
- Summary: `../outputs/llamaparse/summary_llamaparse.json`
