#!/usr/bin/env python3
"""
(<PERSON>) LlamaParse Extractor - Solution 1

☑️ Commercial solution with 9/10 rating
☑️ Fast (~6s per PDF)
☑️ Excellent structure preservation

Agent: B1
"""

import sys
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import traceback

try:
    from llama_parse import LlamaParse
except ImportError:
    print("ERROR: llama-parse not installed. Run: pip install llama-parse")
    sys.exit(1)

# FULLCAPS CONFIG
PDF_SOURCE_DIR = Path("../../test_pdfs")
OUTPUT_DIR = Path("../outputs/llamaparse")
API_KEY = os.getenv("LLAMA_CLOUD_API_KEY")


def extract_pdf_llamaparse(pdf_path: Path) -> Dict[str, Any]:
    """
    Extract text from PDF using LlamaParse.

    Raises:
        FileNotFoundError if PDF doesn't exist then broken
        ValueError if extraction fails then broken
        EnvironmentError if API key missing then broken
    """
    if not pdf_path.exists():
        raise FileNotFoundError(f"PDF not found: {pdf_path}")

    if not API_KEY:
        raise EnvironmentError("LLAMA_CLOUD_API_KEY not set in environment")

    try:
        parser = LlamaParse(
            api_key=API_KEY,
            result_type="markdown",
            verbose=True
        )

        # Parse the PDF
        documents = parser.load_data(str(pdf_path))

        # Extract text from all documents
        pages_data = []
        full_text = []

        for idx, doc in enumerate(documents, start=1):
            text = doc.text
            pages_data.append({
                "page_number": idx,
                "text": text,
                "char_count": len(text)
            })
            full_text.append(text)

        total_chars = sum(p["char_count"] for p in pages_data)

        # LlamaParse has high confidence (9/10 rating)
        confidence = 0.90

        result = {
            "solution": "llamaparse",
            "pdf_file": pdf_path.name,
            "timestamp": datetime.utcnow().isoformat(),
            "num_pages": len(documents),
            "pages": pages_data,
            "full_text_markdown": "\n\n".join(full_text),
            "total_characters": total_chars,
            "confidence_score": confidence,
            "extraction_method": "llamaparse_commercial"
        }

        return result

    except Exception as e:
        raise ValueError(f"LlamaParse extraction failed: {e}")


def main():
    """Extract all PDFs using LlamaParse."""

    print("\n" + "="*60)
    print("LlamaParse Extractor - Solution 1 (Claude)")
    print("Agent: B1 - Commercial (9/10 rating)")
    print("="*60 + "\n")

    # Check API key
    if not API_KEY:
        print("⚠️  LLAMA_CLOUD_API_KEY not set")
        print("    Set environment variable or add to .env file")
        print("    Skipping LlamaParse extraction")
        return

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not PDF_SOURCE_DIR.exists():
        print(f"⚠️  PDF source directory not found: {PDF_SOURCE_DIR}")
        PDF_SOURCE_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓  Created {PDF_SOURCE_DIR}")
        print("\n⏳ No PDFs to process yet. Ready for PDF input.")
        return

    pdf_files = list(PDF_SOURCE_DIR.glob("*.pdf"))

    if not pdf_files:
        print(f"⚠️  No PDF files found in {PDF_SOURCE_DIR}")
        return

    print(f"✓ Found {len(pdf_files)} PDF files\n")

    results = []
    success_count = 0
    fail_count = 0

    for pdf_path in pdf_files:
        print(f"Processing: {pdf_path.name}...")

        try:
            result = extract_pdf_llamaparse(pdf_path)

            # Save JSON
            output_file = OUTPUT_DIR / f"{pdf_path.stem}_llamaparse.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            # Save markdown
            md_file = OUTPUT_DIR / f"{pdf_path.stem}_llamaparse.md"
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(f"# {result['pdf_file']}\n\n")
                f.write(f"**Extracted by**: LlamaParse\n")
                f.write(f"**Pages**: {result['num_pages']}\n")
                f.write(f"**Confidence**: {result['confidence_score']:.2%}\n\n")
                f.write("---\n\n")
                f.write(result['full_text_markdown'])

            results.append(result)
            success_count += 1
            print(f"  ✓ Success ({result['num_pages']} pages, {result['total_characters']:,} chars)")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    # Summary
    summary = {
        "solution": "llamaparse",
        "timestamp": datetime.utcnow().isoformat(),
        "total_pdfs": len(pdf_files),
        "successful": success_count,
        "failed": fail_count,
        "success_rate": success_count / len(pdf_files) if pdf_files else 0,
        "results": results
    }

    with open(OUTPUT_DIR / "summary_llamaparse.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(pdf_files)} successful")
    print(f"Output: {OUTPUT_DIR}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
