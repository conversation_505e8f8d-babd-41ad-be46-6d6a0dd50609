# pyproject.toml (<PERSON>)
# Isolated PDF extraction experiment - all 10 solutions

[project]
name = "pdf-extraction-benchmark"
version = "0.1.0"
description = "Benchmark 10 SotA PDF extraction solutions on IC Memos corpus"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core utilities - no extraction libs (they have conflicts)
    "markdown>=3.6",
    "weasyprint>=62.0",  # MD→PDF conversion
    "pandas>=2.2.0",
    "python-dotenv>=1.0.0",
    "rich>=13.7.0",
    "tqdm>=4.66.0",
]

# NOTE: Each extraction solution has its own venv due to dependency conflicts
# See each solution's directory for installation instructions

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "ruff>=0.8.0",
    "ipython>=8.20.0",
]

[build-system]
requires = ["uv_build>=0.8.12,<0.9.0"]
build-backend = "uv_build"

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
