# Solution 8: pdfplumber (<PERSON>)

**Agent**: B8
**Type**: Table extraction specialist
**Key Feature**: Superior table detection and structure preservation

## Installation

```bash
cd pdfplumber/
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## Usage

```bash
python extract.py
```

## Strengths
- Excellent table extraction
- Layout-aware parsing
- Good for structured documents
- Maintains table structure

## Weaknesses
- Slower than PyPDF2
- No OCR for scanned PDFs
- Memory intensive for large PDFs

## Output
- JSON: `../outputs/pdfplumber/{filename}_pdfplumber.json`
- Markdown: `../outputs/pdfplumber/{filename}_pdfplumber.md`
- Summary: `../outputs/pdfplumber/summary_pdfplumber.json`
