#!/usr/bin/env python3
"""
(<PERSON>) pdfplumber Extractor - Solution 8

☑️ Specialized for table extraction
☑️ Good structured data handling
☑️ Layout-aware

Agent: B8
"""

import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import traceback

try:
    import pdfplumber
except ImportError:
    print("ERROR: pdfplumber not installed. Run: pip install pdfplumber")
    sys.exit(1)

# FULLCAPS CONFIG
PDF_SOURCE_DIR = Path("../../test_pdfs")
OUTPUT_DIR = Path("../outputs/pdfplumber")


def extract_pdf_pdfplumber(pdf_path: Path) -> Dict[str, Any]:
    """
    Extract text and tables from PDF using pdfplumber.

    Raises:
        FileNotFoundError if PDF doesn't exist then broken
        ValueError if extraction fails then broken
    """
    if not pdf_path.exists():
        raise FileNotFoundError(f"PDF not found: {pdf_path}")

    try:
        with pdfplumber.open(pdf_path) as pdf:
            pages = []
            all_tables = []
            full_text = []

            for page_num, page in enumerate(pdf.pages, start=1):
                # Extract text
                text = page.extract_text() or ""

                # Extract tables
                tables = page.extract_tables()
                table_data = []

                for table_idx, table in enumerate(tables):
                    if table:
                        table_data.append({
                            "table_index": table_idx,
                            "rows": len(table),
                            "cols": len(table[0]) if table else 0,
                            "data": table
                        })
                        all_tables.append({
                            "page": page_num,
                            "table_index": table_idx,
                            "data": table
                        })

                pages.append({
                    "page_number": page_num,
                    "text": text,
                    "tables": table_data,
                    "char_count": len(text),
                    "table_count": len(tables)
                })

                full_text.append(text)

            total_chars = sum(p["char_count"] for p in pages)
            total_tables = sum(p["table_count"] for p in pages)

            # Higher confidence if tables extracted
            confidence = min(0.98, 0.75 + (total_tables * 0.05))

            result = {
                "solution": "pdfplumber",
                "pdf_file": pdf_path.name,
                "timestamp": datetime.utcnow().isoformat(),
                "num_pages": len(pdf.pages),
                "pages": pages,
                "full_text": "\n\n".join(full_text),
                "total_characters": total_chars,
                "total_tables": total_tables,
                "all_tables": all_tables,
                "confidence_score": confidence,
                "extraction_method": "pdfplumber_structured"
            }

            return result

    except Exception as e:
        raise ValueError(f"pdfplumber extraction failed: {e}")


def main():
    """Extract all PDFs using pdfplumber."""

    print("\n" + "="*60)
    print("pdfplumber Extractor - Solution 8 (Claude)")
    print("Agent: B8")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not PDF_SOURCE_DIR.exists():
        print(f"⚠️  PDF source directory not found: {PDF_SOURCE_DIR}")
        PDF_SOURCE_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓  Created {PDF_SOURCE_DIR}")
        print("\n⏳ No PDFs to process yet. Ready for PDF input.")
        return

    pdf_files = list(PDF_SOURCE_DIR.glob("*.pdf"))

    if not pdf_files:
        print(f"⚠️  No PDF files found in {PDF_SOURCE_DIR}")
        return

    print(f"✓ Found {len(pdf_files)} PDF files\n")

    results = []
    success_count = 0
    fail_count = 0

    for pdf_path in pdf_files:
        print(f"Processing: {pdf_path.name}...")

        try:
            result = extract_pdf_pdfplumber(pdf_path)

            # Save JSON
            output_file = OUTPUT_DIR / f"{pdf_path.stem}_pdfplumber.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            # Save markdown
            md_file = OUTPUT_DIR / f"{pdf_path.stem}_pdfplumber.md"
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(f"# {result['pdf_file']}\n\n")
                f.write(f"**Extracted by**: pdfplumber\n")
                f.write(f"**Pages**: {result['num_pages']}\n")
                f.write(f"**Tables**: {result['total_tables']}\n")
                f.write(f"**Confidence**: {result['confidence_score']:.2%}\n\n")
                f.write("---\n\n")
                f.write(result['full_text'])

                if result['all_tables']:
                    f.write("\n\n## Extracted Tables\n\n")
                    for table_info in result['all_tables']:
                        f.write(f"### Table {table_info['table_index']} (Page {table_info['page']})\n\n")
                        f.write("| " + " | ".join(str(c) for c in table_info['data'][0]) + " |\n")
                        f.write("| " + " | ".join("---" for _ in table_info['data'][0]) + " |\n")
                        for row in table_info['data'][1:]:
                            f.write("| " + " | ".join(str(c) if c else "" for c in row) + " |\n")
                        f.write("\n")

            results.append(result)
            success_count += 1
            print(f"  ✓ Success ({result['num_pages']} pages, {result['total_tables']} tables)")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    # Summary
    summary = {
        "solution": "pdfplumber",
        "timestamp": datetime.utcnow().isoformat(),
        "total_pdfs": len(pdf_files),
        "successful": success_count,
        "failed": fail_count,
        "success_rate": success_count / len(pdf_files) if pdf_files else 0,
        "results": results
    }

    with open(OUTPUT_DIR / "summary_pdfplumber.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(pdf_files)} successful")
    print(f"Output: {OUTPUT_DIR}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
