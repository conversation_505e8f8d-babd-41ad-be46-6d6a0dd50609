#!/bin/bash
# (<PERSON>) Run All 10 Markdown Parsing Solutions

echo "========================================"
echo "Running All 10 Markdown Parsing Solutions"
echo "========================================"

cd "$(dirname "$0")"

# Solution 1: LLM (if API key available)
if [ -n "$ANTHROPIC_API_KEY" ]; then
    echo -e "\n[1/10] Running LLM Structured Output..."
    cd solution1_llm && python extract.py && cd ..
else
    echo -e "\n[1/10] Skipping LLM (no API key)"
fi

# Solution 2: Regex
echo -e "\n[2/10] Running Regex Pattern Matching..."
cd solution2_regex && python extract.py && cd ..

# Solution 3: NER
echo -e "\n[3/10] Running NER Entity Extraction..."
cd solution3_ner && python extract.py && cd ..

# Solution 4: LangChain
echo -e "\n[4/10] Running LangChain Document Loader..."
cd solution4_langchain && python extract.py && cd ..

# Solution 5: Custom Business Logic
echo -e "\n[5/10] Running Custom Business Parser..."
cd solution5_custom && python extract.py && cd ..

# Solution 6: AST
echo -e "\n[6/10] Running Markdown AST Parser..."
cd solution6_ast && python extract.py && cd ..

# Solution 7: Hybrid
echo -e "\n[7/10] Running Hybrid LLM+Rules..."
cd solution7_hybrid && python extract.py && cd ..

# Solution 8: Few-Shot
echo -e "\n[8/10] Running Few-Shot Learning..."
cd solution8_fewshot && python extract.py && cd ..

# Solution 9: Prompt Engineering
echo -e "\n[9/10] Running Prompt Engineering..."
cd solution9_prompt && python extract.py && cd ..

# Solution 10: Ensemble
echo -e "\n[10/10] Running Ensemble Voting..."
cd solution10_ensemble && python extract.py && cd ..

echo -e "\n========================================"
echo "✅ All 10 Solutions Complete!"
echo "Check outputs/ directory for results"
echo "========================================"
