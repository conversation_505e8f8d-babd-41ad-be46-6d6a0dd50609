#!/usr/bin/env python3
"""
(<PERSON>) Solution 8: Few-Shot Learning

☑️ Stub - would use examples to guide extraction
☑️ Learning from sample memos

Agent: B8
"""

import sys
import json
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo
sys.path.append(str(Path(__file__).parent.parent / "solution2_regex"))
from extract import parse_md_with_regex

MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution8_fewshot")


def parse_md_fewshot(md_path: Path) -> ICMemo:
    """Few-shot learning approach (stub uses regex)."""
    memo = parse_md_with_regex(md_path)
    memo.extraction_method = "fewshot_learning_stub"
    memo.confidence_score = 0.82
    return memo


def main():
    print("\n" + "="*60)
    print("Solution 8: Few-Shot Learning (Stub)")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    md_files = list(MD_SOURCE_DIR.glob("*.md")) if MD_SOURCE_DIR.exists() else []

    results = []
    for md_path in md_files:
        try:
            memo = parse_md_fewshot(md_path)
            output_file = OUTPUT_DIR / f"{md_path.stem}_fewshot.json"
            with open(output_file, 'w') as f:
                f.write(memo.model_dump_json(indent=2))
            results.append(memo)
            print(f"✓ {md_path.name}: {len(memo.deals)} deals")
        except Exception as e:
            print(f"✗ {md_path.name}: {e}")

    with open(OUTPUT_DIR / "summary_fewshot.json", 'w') as f:
        json.dump({"solution": "fewshot", "total": len(results)}, f)

    print(f"\nProcessed {len(results)} files")


if __name__ == "__main__":
    main()
