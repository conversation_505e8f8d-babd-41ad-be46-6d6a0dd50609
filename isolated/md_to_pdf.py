#!/usr/bin/env python3
"""
(Claude) MD→PDF Converter for Test Data Generation

☑️ REQUIREMENT: Generate synthetic test PDFs from ground-truth MD files
☑️ PURPOSE: Create realistic test corpus for validating 10 extraction solutions
☑️ SCOPE: Read MD from ../Access IC Memos/, write PDFs to outputs/test_pdfs/

This creates a ground-truth validation set.
"""

import sys
from pathlib import Path
import markdown
from weasyprint import HTML, CSS
from tqdm import tqdm
from rich import print as rprint
from rich.console import Console

console = Console()

# FULLCAPS CONFIG at top
SOURCE_DIR = Path("../Access IC Memos")
OUTPUT_DIR = Path("outputs/test_pdfs")
CSS_TEMPLATE = """
@page {
    size: A4;
    margin: 1in;
}
body {
    font-family: 'Georgia', serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #333;
}
h1 {
    font-size: 18pt;
    margin-top: 24pt;
    margin-bottom: 12pt;
    page-break-before: always;
}
h2 {
    font-size: 14pt;
    margin-top: 18pt;
    margin-bottom: 9pt;
}
table {
    border-collapse: collapse;
    width: 100%;
    margin: 12pt 0;
}
th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
"""


def convert_md_to_pdf(md_file: Path, output_file: Path) -> bool:
    """
    Convert single markdown file to PDF with realistic formatting.

    Args:
        md_file: Source markdown file
        output_file: Destination PDF file

    Returns:
        bool: True if successful

    Raises:
        - IOError if markdown file read fails then broken
        - ValueError if PDF conversion fails then broken
    """
    try:
        # Read markdown
        md_content = md_file.read_text(encoding="utf-8")

        # Convert to HTML
        html_content = markdown.markdown(
            md_content,
            extensions=["tables", "fenced_code", "nl2br"]
        )

        # Wrap in proper HTML structure
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{md_file.stem}</title>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        # Convert to PDF
        HTML(string=full_html).write_pdf(
            output_file,
            stylesheets=[CSS(string=CSS_TEMPLATE)]
        )

        return True

    except IOError as e:
        console.print(f"[red]✗ Failed to read {md_file.name}: {e}[/red]")
        raise IOError(f"Markdown file read failed: {md_file}")
    except Exception as e:
        console.print(f"[red]✗ PDF conversion failed for {md_file.name}: {e}[/red]")
        raise ValueError(f"PDF conversion failed: {md_file}")


def main():
    """Generate test PDFs from all markdown files."""

    console.print("\n[bold cyan]╔══════════════════════════════════════════════╗[/bold cyan]")
    console.print("[bold cyan]║   MD→PDF Test Data Generator (Claude)       ║[/bold cyan]")
    console.print("[bold cyan]╚══════════════════════════════════════════════╝[/bold cyan]\n")

    # Verify source directory exists
    if not SOURCE_DIR.exists():
        console.print(f"[red]✗ Source directory not found: {SOURCE_DIR}[/red]")
        raise FileNotFoundError(f"Source directory missing: {SOURCE_DIR}")

    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # Find all markdown files
    md_files = list(SOURCE_DIR.glob("*.md"))

    if not md_files:
        console.print(f"[yellow]⚠ No markdown files found in {SOURCE_DIR}[/yellow]")
        raise FileNotFoundError(f"No markdown files in {SOURCE_DIR}")

    console.print(f"[green]✓ Found {len(md_files)} markdown files[/green]")
    console.print(f"[blue]→ Source: {SOURCE_DIR}[/blue]")
    console.print(f"[blue]→ Output: {OUTPUT_DIR}[/blue]\n")

    # Convert each file
    success_count = 0
    fail_count = 0

    with console.status("[bold green]Converting markdown to PDF...") as status:
        for md_file in tqdm(md_files, desc="Converting", unit="file"):
            output_file = OUTPUT_DIR / f"{md_file.stem}.pdf"

            try:
                convert_md_to_pdf(md_file, output_file)
                success_count += 1
                console.print(f"[green]✓ {md_file.name} → {output_file.name}[/green]")
            except Exception as e:
                fail_count += 1
                console.print(f"[red]✗ Failed: {md_file.name}[/red]")
                # Continue processing remaining files

    # Summary
    console.print(f"\n[bold]Summary:[/bold]")
    console.print(f"  [green]✓ Success: {success_count}[/green]")

    if fail_count > 0:
        console.print(f"  [red]✗ Failed: {fail_count}[/red]")

    if success_count == 0:
        console.print("[red]✗ All conversions failed[/red]")
        raise RuntimeError("All PDF conversions failed")

    console.print(f"\n[bold green]✓ Test PDF corpus generated![/bold green]")
    console.print(f"[blue]→ Location: {OUTPUT_DIR.absolute()}[/blue]\n")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        console.print(f"\n[bold red]✗ Fatal error: {e}[/bold red]")
        sys.exit(1)
