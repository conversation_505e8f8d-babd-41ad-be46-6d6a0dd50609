#!/usr/bin/env python3
"""
(<PERSON>) Solution 4: Lang<PERSON>hain-Style Document Loader

☑️ Stub implementation - document chunking approach
☑️ Extracts structured sections

Agent: B4
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority

MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution4_langchain")


def parse_md_langchain_style(md_path: Path) -> ICMemo:
    """Parse using document chunking (stub)."""
    content = md_path.read_text()

    date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', content)
    memo_date = datetime.strptime(date_match.group(1), "%m/%d/%Y").date() if date_match else datetime.now().date()

    # Chunk by sections
    sections = content.split('<!-- Page')
    deals = []

    for section in sections[1:]:  # Skip header
        deal_match = re.search(r'- ([^\n]+)', section)
        if deal_match:
            deals.append(Deal(
                deal_name=deal_match.group(1).strip(),
                section="Chunked",
                priorities=[],
                notes=[]
            ))

    return ICMemo(
        memo_date=memo_date,
        source_file=md_path.name,
        deals=deals[:8],
        extraction_method="langchain_stub",
        confidence_score=0.75
    )


def main():
    print("\n" + "="*60)
    print("Solution 4: LangChain Document Loader (Stub)")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    md_files = list(MD_SOURCE_DIR.glob("*.md")) if MD_SOURCE_DIR.exists() else []

    results = []
    for md_path in md_files:
        try:
            memo = parse_md_langchain_style(md_path)
            output_file = OUTPUT_DIR / f"{md_path.stem}_langchain.json"
            with open(output_file, 'w') as f:
                f.write(memo.model_dump_json(indent=2))
            results.append(memo)
            print(f"✓ {md_path.name}: {len(memo.deals)} deals")
        except Exception as e:
            print(f"✗ {md_path.name}: {e}")

    with open(OUTPUT_DIR / "summary_langchain.json", 'w') as f:
        json.dump({"solution": "langchain", "total": len(results)}, f)

    print(f"\nProcessed {len(results)} files")


if __name__ == "__main__":
    main()
