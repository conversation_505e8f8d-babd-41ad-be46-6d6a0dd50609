#!/usr/bin/env python3
"""
(<PERSON>) Solution 5: Custom Business Logic Parser

☑️ Domain-specific rules for IC memos
☑️ Fast and deterministic
☑️ Tailored to memo structure

Agent: B5
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
from typing import List
import traceback

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority

# FULLCAPS CONFIG
MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution5_custom")


def parse_md_custom(md_path: Path) -> ICMemo:
    """
    Parse markdown using custom business logic.

    Understands IC memo structure:
    - Section 1: Deals in Process
    - Each deal has 5 subsections
    - Bullet points with sub-bullets
    """
    if not md_path.exists():
        raise FileNotFoundError(f"Markdown file not found: {md_path}")

    try:
        lines = md_path.read_text(encoding='utf-8').split('\n')

        # Extract date from first few lines
        memo_date = None
        for line in lines[:10]:
            date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', line)
            if date_match:
                memo_date = datetime.strptime(date_match.group(1), "%m/%d/%Y").date()
                break

        if not memo_date:
            # Try filename
            fn_match = re.search(r'(\d{2}\.\d{2}\.\d{4})', md_path.name)
            if fn_match:
                memo_date = datetime.strptime(fn_match.group(1), "%m.%d.%Y").date()
            else:
                memo_date = datetime.now().date()

        deals = []
        current_deal = None
        current_section = None
        in_priorities = False

        for i, line in enumerate(lines):
            stripped = line.strip()

            # New deal starts with "- DealName" at start of line
            if line.startswith('- ') and not line.startswith('  '):
                # Save previous deal
                if current_deal:
                    deals.append(current_deal)

                deal_name = line[2:].strip()
                current_deal = {
                    "name": deal_name,
                    "priorities": [],
                    "notes": []
                }
                in_priorities = False

            # Section markers
            elif stripped.startswith('1. Key Priorities'):
                in_priorities = True
                current_section = "Key Priorities"
            elif stripped.startswith('2. Strategic Initiatives'):
                in_priorities = False
                current_section = "Strategic Initiatives"
            elif stripped.startswith('3. Human Capital'):
                current_section = "Human Capital"
            elif stripped.startswith('4. Balance Sheet'):
                current_section = "Balance Sheet"
            elif stripped.startswith('5. M&A'):
                current_section = "M&A"

            # Priority items (indented a., b., c., etc.)
            elif current_deal and line.startswith('     a.'):
                text = line[7:].strip()
                if text and text != 'N/A':
                    current_deal["priorities"].append({
                        "text": text,
                        "category": current_section or "Unknown"
                    })
            elif current_deal and re.match(r'     [b-z]\.', line):
                text = line[7:].strip()
                if text and text != 'N/A':
                    current_deal["priorities"].append({
                        "text": text,
                        "category": current_section or "Unknown"
                    })

        # Don't forget last deal
        if current_deal:
            deals.append(current_deal)

        # Convert to schema
        memo_deals = []
        for d in deals:
            priorities = [
                Priority(description=p["text"], category=p.get("category"))
                for p in d["priorities"]
            ]
            memo_deals.append(Deal(
                deal_name=d["name"],
                section="Deals in Process",
                priorities=priorities,
                notes=d.get("notes", [])
            ))

        memo = ICMemo(
            memo_date=memo_date,
            source_file=md_path.name,
            deals=memo_deals,
            extraction_method="custom_business_logic",
            confidence_score=0.85  # Good confidence - tailored to structure
        )

        return memo

    except Exception as e:
        raise ValueError(f"Custom parser extraction failed: {e}")


def main():
    """Extract all markdown files using custom parser."""

    print("\n" + "="*60)
    print("Solution 5: Custom Business Logic Parser")
    print("Agent: B5")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not MD_SOURCE_DIR.exists():
        print(f"✗ Source directory not found: {MD_SOURCE_DIR}")
        raise FileNotFoundError(f"Source not found: {MD_SOURCE_DIR}")

    md_files = list(MD_SOURCE_DIR.glob("*.md"))

    if not md_files:
        print(f"⚠️  No markdown files found")
        return

    print(f"✓ Found {len(md_files)} markdown files\n")

    results = []
    success_count = 0
    fail_count = 0

    for md_path in md_files:
        print(f"Processing: {md_path.name}...")

        try:
            memo = parse_md_custom(md_path)

            output_file = OUTPUT_DIR / f"{md_path.stem}_custom.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(memo.model_dump_json(indent=2))

            results.append(memo)
            success_count += 1
            print(f"  ✓ Extracted {len(memo.deals)} deals")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    summary = {
        "solution": "custom_business_logic",
        "timestamp": datetime.utcnow().isoformat(),
        "total_files": len(md_files),
        "successful": success_count,
        "failed": fail_count,
        "total_deals_extracted": sum(len(m.deals) for m in results)
    }

    with open(OUTPUT_DIR / "summary_custom.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(md_files)} successful")
    print(f"Total deals extracted: {summary['total_deals_extracted']}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
