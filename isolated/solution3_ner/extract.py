#!/usr/bin/env python3
"""
(<PERSON>) Solution 3: NER + Entity Extraction

☑️ Stub implementation - extracts named entities
☑️ Uses simple pattern matching for entities
☑️ Fast entity recognition

Agent: B3
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
import traceback

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority

MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution3_ner")


def parse_md_with_ner(md_path: Path) -> ICMemo:
    """Parse using entity extraction (stub)."""
    if not md_path.exists():
        raise FileNotFoundError(f"Not found: {md_path}")

    content = md_path.read_text()

    # Extract date
    date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', content)
    memo_date = datetime.strptime(date_match.group(1), "%m/%d/%Y").date() if date_match else datetime.now().date()

    # Simple entity extraction - find capitalized phrases
    # (Real NER would use spaCy, but this is a stub)
    lines = content.split('\n')
    deals = []

    for line in lines:
        if line.startswith('- ') and not line.startswith('  '):
            deal_name = line[2:].strip()
            if deal_name and len(deal_name) > 3:
                deals.append(Deal(
                    deal_name=deal_name,
                    section="Extracted",
                    priorities=[],
                    notes=[]
                ))

    return ICMemo(
        memo_date=memo_date,
        source_file=md_path.name,
        deals=deals[:10],  # Limit
        extraction_method="ner_stub",
        confidence_score=0.70
    )


def main():
    print("\n" + "="*60)
    print("Solution 3: NER Entity Extraction (Stub)")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    md_files = list(MD_SOURCE_DIR.glob("*.md")) if MD_SOURCE_DIR.exists() else []

    if not md_files:
        print("No files found")
        return

    results = []
    for md_path in md_files:
        try:
            memo = parse_md_with_ner(md_path)
            output_file = OUTPUT_DIR / f"{md_path.stem}_ner.json"
            with open(output_file, 'w') as f:
                f.write(memo.model_dump_json(indent=2))
            results.append(memo)
            print(f"✓ {md_path.name}: {len(memo.deals)} deals")
        except Exception as e:
            print(f"✗ {md_path.name}: {e}")

    with open(OUTPUT_DIR / "summary_ner.json", 'w') as f:
        json.dump({"solution": "ner", "total": len(results)}, f)

    print(f"\nProcessed {len(results)} files")


if __name__ == "__main__":
    main()
