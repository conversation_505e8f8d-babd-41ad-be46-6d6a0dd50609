#!/usr/bin/env python3
"""
(<PERSON>) Solution 6: Markdown AST Parser

☑️ Parses markdown abstract syntax tree
☑️ Structure-aware extraction
☑️ Handles nested content

Agent: B6
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
from typing import List
import traceback

try:
    import markdown
    from markdown.treeprocessors import Treeprocessor
    from markdown.extensions import Extension
except ImportError:
    print("ERROR: markdown not installed. Run: pip install markdown")
    sys.exit(1)

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority

# FULLCAPS CONFIG
MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution6_ast")


class ICMemoExtractor(Treeprocessor):
    """Custom tree processor to extract deal structure."""

    def run(self, root):
        """Process the markdown tree."""
        self.deals = []
        self.current_deal = None
        self.current_section = None

        for child in root:
            if child.tag == 'ul':
                # Process list items (deals)
                for li in child:
                    if li.tag == 'li':
                        self._process_list_item(li)

        return root

    def _process_list_item(self, li):
        """Process a list item."""
        # Check if this is a top-level deal (not nested)
        text = ''.join(li.itertext())

        if text and not li.find('.//ul') is None:
            # Has nested list - likely a deal
            deal_name = text.split('\n')[0].strip()
            if deal_name:
                self.current_deal = {
                    "name": deal_name,
                    "priorities": []
                }
                self.deals.append(self.current_deal)

                # Process nested lists for priorities
                for nested_ul in li.findall('.//ul'):
                    for nested_li in nested_ul:
                        priority_text = ''.join(nested_li.itertext()).strip()
                        if priority_text and self.current_deal:
                            self.current_deal["priorities"].append(priority_text)


class ICMemoExtension(Extension):
    """Markdown extension for IC memo extraction."""

    def extendMarkdown(self, md):
        """Add the tree processor."""
        extractor = ICMemoExtractor(md)
        md.treeprocessors.register(extractor, 'icmemo', 10)
        md.extractor = extractor


def parse_md_with_ast(md_path: Path) -> ICMemo:
    """
    Parse markdown using AST parsing.

    Raises:
        FileNotFoundError if MD doesn't exist then broken
        ValueError if extraction fails then broken
    """
    if not md_path.exists():
        raise FileNotFoundError(f"Markdown file not found: {md_path}")

    try:
        content = md_path.read_text(encoding='utf-8')

        # Extract date
        date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', content)
        if date_match:
            memo_date = datetime.strptime(date_match.group(1), "%m/%d/%Y").date()
        else:
            fn_match = re.search(r'(\d{2}\.\d{2}\.\d{4})', md_path.name)
            if fn_match:
                memo_date = datetime.strptime(fn_match.group(1), "%m.%d.%Y").date()
            else:
                memo_date = datetime.now().date()

        # Parse with custom extension
        md = markdown.Markdown(extensions=[ICMemoExtension()])
        html = md.convert(content)

        # Extract deals from processor
        deals_data = getattr(md, 'extractor', None)
        if deals_data:
            deals_raw = deals_data.deals
        else:
            deals_raw = []

        # Convert to schema
        deals = []
        for d in deals_raw:
            priorities = [
                Priority(description=p, category="Unknown")
                for p in d.get("priorities", [])[:10]  # Limit
            ]
            deals.append(Deal(
                deal_name=d["name"],
                section="Deals in Process",
                priorities=priorities,
                notes=[]
            ))

        memo = ICMemo(
            memo_date=memo_date,
            source_file=md_path.name,
            deals=deals,
            extraction_method="markdown_ast_parser",
            confidence_score=0.80  # Good confidence - structure-aware
        )

        return memo

    except Exception as e:
        raise ValueError(f"AST parser extraction failed: {e}")


def main():
    """Extract all markdown files using AST parser."""

    print("\n" + "="*60)
    print("Solution 6: Markdown AST Parser")
    print("Agent: B6")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not MD_SOURCE_DIR.exists():
        print(f"✗ Source directory not found: {MD_SOURCE_DIR}")
        raise FileNotFoundError(f"Source not found: {MD_SOURCE_DIR}")

    md_files = list(MD_SOURCE_DIR.glob("*.md"))

    if not md_files:
        print(f"⚠️  No markdown files found")
        return

    print(f"✓ Found {len(md_files)} markdown files\n")

    results = []
    success_count = 0
    fail_count = 0

    for md_path in md_files:
        print(f"Processing: {md_path.name}...")

        try:
            memo = parse_md_with_ast(md_path)

            output_file = OUTPUT_DIR / f"{md_path.stem}_ast.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(memo.model_dump_json(indent=2))

            results.append(memo)
            success_count += 1
            print(f"  ✓ Extracted {len(memo.deals)} deals")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    summary = {
        "solution": "markdown_ast",
        "timestamp": datetime.utcnow().isoformat(),
        "total_files": len(md_files),
        "successful": success_count,
        "failed": fail_count,
        "total_deals_extracted": sum(len(m.deals) for m in results)
    }

    with open(OUTPUT_DIR / "summary_ast.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(md_files)} successful")
    print(f"Total deals extracted: {summary['total_deals_extracted']}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
