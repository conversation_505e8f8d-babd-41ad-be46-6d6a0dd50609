#!/usr/bin/env python3
"""
(<PERSON>) Master completion script - runs all 10 solutions, comparison, database, report

☑️ Runs all 10 markdown parsers on 48 files
☑️ Creates comparison framework
☑️ Builds SQLite database
☑️ Generates final report

Usage: python complete.py
"""

import subprocess
import sys
import json
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

console = Console()

# Paths
MD_SOURCE = Path("../Access IC Memos")
OUTPUT_DIR = Path("outputs")
SOLUTIONS = [
    ("solution1_llm", "LLM Structured Output", "ANTHROPIC_API_KEY"),
    ("solution2_regex", "Regex Pattern Matching", None),
    ("solution3_ner", "NER Entity Extraction", None),
    ("solution4_langchain", "LangChain Loader", None),
    ("solution5_custom", "Custom Business Logic", None),
    ("solution6_ast", "Markdown AST Parser", None),
    ("solution7_hybrid", "Hybrid LLM+Rules", None),
    ("solution8_fewshot", "Few-Shot Learning", None),
    ("solution9_prompt", "Prompt Engineering", None),
    ("solution10_ensemble", "Ensemble Voting", None),
]

def run_solution(solution_dir: str, name: str, api_key_var: str = None) -> dict:
    """Run a single solution and return stats."""
    import os

    if api_key_var and not os.getenv(api_key_var):
        return {"skipped": True, "reason": f"No {api_key_var}"}

    extract_path = Path(solution_dir) / "extract.py"
    if not extract_path.exists():
        return {"error": True, "reason": "extract.py not found"}

    try:
        result = subprocess.run(
            [sys.executable, "extract.py"],
            cwd=solution_dir,
            capture_output=True,
            text=True,
            timeout=300
        )

        # Count output files
        output_path = OUTPUT_DIR / solution_dir.split("_")[1]
        if output_path.exists():
            files = list(output_path.glob("*.json"))
            count = len([f for f in files if not f.name.startswith("summary")])
        else:
            count = 0

        return {
            "success": result.returncode == 0,
            "files_processed": count,
            "stderr": result.stderr if result.returncode != 0 else ""
        }
    except subprocess.TimeoutExpired:
        return {"error": True, "reason": "Timeout (5min)"}
    except Exception as e:
        return {"error": True, "reason": str(e)}

def create_comparison() -> dict:
    """Compare all solution outputs."""
    comparison = {
        "timestamp": datetime.now().isoformat(),
        "solutions": {},
        "agreement_matrix": {}
    }

    for solution_dir, name, _ in SOLUTIONS:
        output_path = OUTPUT_DIR / solution_dir.split("_")[1]
        if output_path.exists():
            files = list(output_path.glob("*[!summary]*.json"))
            total_deals = 0
            for f in files:
                try:
                    data = json.loads(f.read_text())
                    total_deals += len(data.get("deals", []))
                except:
                    pass

            comparison["solutions"][name] = {
                "files": len(files),
                "total_deals": total_deals,
                "avg_deals_per_file": round(total_deals / len(files), 2) if files else 0
            }

    # Save comparison
    comp_path = OUTPUT_DIR / "comparison.json"
    comp_path.parent.mkdir(parents=True, exist_ok=True)
    comp_path.write_text(json.dumps(comparison, indent=2))

    return comparison

def create_database() -> dict:
    """Create SQLite database with all extractions."""
    import sqlite3

    db_path = OUTPUT_DIR / "ic_memos.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create tables
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS memos (
            id INTEGER PRIMARY KEY,
            memo_date TEXT,
            source_file TEXT,
            extraction_method TEXT,
            confidence_score REAL
        )
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS deals (
            id INTEGER PRIMARY KEY,
            memo_id INTEGER,
            deal_name TEXT,
            section TEXT,
            FOREIGN KEY (memo_id) REFERENCES memos(id)
        )
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS priorities (
            id INTEGER PRIMARY KEY,
            deal_id INTEGER,
            description TEXT,
            category TEXT,
            FOREIGN KEY (deal_id) REFERENCES deals(id)
        )
    """)

    # Import data from best solution (solution2_regex)
    total_imported = 0
    output_path = OUTPUT_DIR / "solution2_regex"
    if output_path.exists():
        for json_file in output_path.glob("*[!summary]*.json"):
            try:
                data = json.loads(json_file.read_text())
                cursor.execute(
                    "INSERT INTO memos (memo_date, source_file, extraction_method, confidence_score) VALUES (?, ?, ?, ?)",
                    (data.get("memo_date"), data.get("source_file"), data.get("extraction_method"), data.get("confidence_score", 0.0))
                )
                memo_id = cursor.lastrowid

                for deal in data.get("deals", []):
                    cursor.execute(
                        "INSERT INTO deals (memo_id, deal_name, section) VALUES (?, ?, ?)",
                        (memo_id, deal.get("deal_name"), deal.get("section"))
                    )
                    deal_id = cursor.lastrowid

                    for priority in deal.get("priorities", []):
                        cursor.execute(
                            "INSERT INTO priorities (deal_id, description, category) VALUES (?, ?, ?)",
                            (deal_id, priority.get("description"), priority.get("category"))
                        )

                total_imported += 1
            except Exception as e:
                console.print(f"[yellow]⚠ Skipped {json_file.name}: {e}[/yellow]")

    conn.commit()
    conn.close()

    return {"database": str(db_path), "memos_imported": total_imported}

def generate_report(results: dict, comparison: dict, db_info: dict):
    """Generate final report."""
    report_path = OUTPUT_DIR / "FINAL_REPORT.md"

    report = f"""# IC Memo Extraction - Final Report

**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Execution Summary

"""

    # Solution results
    report += "### Solutions Executed\n\n"
    report += "| Solution | Status | Files Processed |\n"
    report += "|----------|--------|----------------|\n"

    for (solution_dir, name, _), result in zip(SOLUTIONS, results):
        if result.get("skipped"):
            status = f"⊘ Skipped ({result['reason']})"
            files = "-"
        elif result.get("error"):
            status = f"✗ Error"
            files = "-"
        elif result.get("success"):
            status = "✓ Success"
            files = str(result.get("files_processed", 0))
        else:
            status = "?"
            files = "-"

        report += f"| {name} | {status} | {files} |\n"

    # Comparison
    report += "\n### Comparison Results\n\n"
    report += "| Solution | Files | Total Deals | Avg Deals/File |\n"
    report += "|----------|-------|-------------|----------------|\n"

    for name, stats in comparison.get("solutions", {}).items():
        report += f"| {name} | {stats['files']} | {stats['total_deals']} | {stats['avg_deals_per_file']:.2f} |\n"

    # Database
    report += f"\n### Database\n\n"
    report += f"- **Location**: `{db_info['database']}`\n"
    report += f"- **Memos Imported**: {db_info['memos_imported']}\n"
    report += f"\n### Query Examples\n\n"
    report += "```sql\n"
    report += "-- All deals from Q4 2024\n"
    report += "SELECT * FROM deals d JOIN memos m ON d.memo_id = m.id WHERE m.memo_date LIKE '%2024';\n"
    report += "\n-- Top priorities across all deals\n"
    report += "SELECT p.description, COUNT(*) as frequency FROM priorities p GROUP BY p.description ORDER BY frequency DESC LIMIT 10;\n"
    report += "```\n"

    report += "\n## Next Steps\n\n"
    report += "1. Query the database: `sqlite3 outputs/ic_memos.db`\n"
    report += "2. Review comparison: `cat outputs/comparison.json`\n"
    report += "3. Build \"Talk to My Data\" interface\n"

    report_path.write_text(report)
    return report_path

def main():
    console.print("\n[bold cyan]IC Memo Extraction - Complete Run[/bold cyan]\n")

    # Phase 1: Run all solutions
    console.print("[bold]Phase 1: Running Solutions[/bold]")
    results = []

    with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
        for solution_dir, name, api_key_var in SOLUTIONS:
            task = progress.add_task(f"[cyan]{name}...", total=None)
            result = run_solution(solution_dir, name, api_key_var)
            results.append(result)

            if result.get("success"):
                console.print(f"  ✓ {name}: {result.get('files_processed', 0)} files")
            elif result.get("skipped"):
                console.print(f"  ⊘ {name}: {result['reason']}")
            else:
                console.print(f"  ✗ {name}: {result.get('reason', 'Error')}")

    # Phase 2: Comparison
    console.print("\n[bold]Phase 2: Creating Comparison[/bold]")
    comparison = create_comparison()
    console.print(f"  ✓ Compared {len(comparison['solutions'])} solutions")

    # Phase 3: Database
    console.print("\n[bold]Phase 3: Building Database[/bold]")
    db_info = create_database()
    console.print(f"  ✓ Database created: {db_info['database']}")
    console.print(f"  ✓ Imported {db_info['memos_imported']} memos")

    # Phase 4: Report
    console.print("\n[bold]Phase 4: Generating Report[/bold]")
    report_path = generate_report(results, comparison, db_info)
    console.print(f"  ✓ Report saved: {report_path}")

    # Summary table
    console.print("\n[bold green]✓ Complete![/bold green]\n")

    table = Table(title="Final Summary")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")

    successful = sum(1 for r in results if r.get("success"))
    total_files = sum(r.get("files_processed", 0) for r in results if r.get("success"))

    table.add_row("Solutions Run", f"{successful}/{len(SOLUTIONS)}")
    table.add_row("Total Extractions", str(total_files))
    table.add_row("Database", str(db_info['database']))
    table.add_row("Report", str(report_path))

    console.print(table)
    console.print("\n[dim]View full report: cat outputs/FINAL_REPORT.md[/dim]\n")

if __name__ == "__main__":
    main()
