# Solution 7: PyPDF2 (<PERSON>)

**Agent**: B7
**Type**: Lightweight baseline
**Key Feature**: Simple, no external dependencies

## Installation

```bash
cd pypdf2/
python -m venv .venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
pip install -r requirements.txt
```

## Usage

```bash
python extract.py
```

## Strengths
- Very lightweight
- Fast for simple text extraction
- No external dependencies (pure Python)
- Good for text-based PDFs

## Weaknesses
- Limited table extraction
- No OCR capability
- Struggles with complex layouts
- Basic metadata only

## Output
- JSON: `../outputs/pypdf2/{filename}_pypdf2.json`
- Markdown: `../outputs/pypdf2/{filename}_pypdf2.md`
- Summary: `../outputs/pypdf2/summary_pypdf2.json`
