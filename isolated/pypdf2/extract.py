#!/usr/bin/env python3
"""
(Claude) PyPDF2 Extractor - Solution 7

☑️ Lightweight baseline PDF text extraction
☑️ No external dependencies beyond PyPDF2
☑️ Fast and simple

Agent: B7
"""

import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import traceback

try:
    from PyPDF2 import PdfReader
except ImportError:
    print("ERROR: PyPDF2 not installed. Run: pip install pypdf2")
    sys.exit(1)

# FULLCAPS CONFIG
PDF_SOURCE_DIR = Path("../../test_pdfs")  # Will create test PDFs later
OUTPUT_DIR = Path("../outputs/pypdf2")
CONFIDENCE_THRESHOLD = 0.85


def extract_pdf_pypdf2(pdf_path: Path) -> Dict[str, Any]:
    """
    Extract text from PDF using PyPDF2.

    Args:
        pdf_path: Path to PDF file

    Returns:
        dict with extracted text, metadata, confidence

    Raises:
        FileNotFoundError if PDF doesn't exist then broken
        ValueError if extraction fails then broken
    """
    if not pdf_path.exists():
        raise FileNotFoundError(f"PDF not found: {pdf_path}")

    try:
        reader = PdfReader(pdf_path)

        # Extract metadata
        metadata = reader.metadata or {}

        # Extract text from all pages
        pages = []
        full_text = []

        for page_num, page in enumerate(reader.pages, start=1):
            text = page.extract_text()
            pages.append({
                "page_number": page_num,
                "text": text,
                "char_count": len(text)
            })
            full_text.append(text)

        # Calculate confidence based on text extraction success
        total_chars = sum(p["char_count"] for p in pages)
        confidence = min(0.95, 0.5 + (total_chars / 10000))  # Simple heuristic

        result = {
            "solution": "pypdf2",
            "pdf_file": pdf_path.name,
            "timestamp": datetime.utcnow().isoformat(),
            "num_pages": len(reader.pages),
            "metadata": {
                "title": metadata.get("/Title", ""),
                "author": metadata.get("/Author", ""),
                "subject": metadata.get("/Subject", ""),
                "creator": metadata.get("/Creator", ""),
            },
            "pages": pages,
            "full_text": "\n\n".join(full_text),
            "total_characters": total_chars,
            "confidence_score": confidence,
            "extraction_method": "pypdf2_basic"
        }

        return result

    except Exception as e:
        raise ValueError(f"PyPDF2 extraction failed: {e}")


def main():
    """Extract all PDFs using PyPDF2."""

    print("\n" + "="*60)
    print("PyPDF2 Extractor - Solution 7 (Claude)")
    print("Agent: B7")
    print("="*60 + "\n")

    # Create output directory
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # Find PDFs
    if not PDF_SOURCE_DIR.exists():
        print(f"⚠️  PDF source directory not found: {PDF_SOURCE_DIR}")
        print("    Creating placeholder for when PDFs are available...")
        PDF_SOURCE_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✓  Created {PDF_SOURCE_DIR}")
        print("\n⏳ No PDFs to process yet. Ready for PDF input.")
        return

    pdf_files = list(PDF_SOURCE_DIR.glob("*.pdf"))

    if not pdf_files:
        print(f"⚠️  No PDF files found in {PDF_SOURCE_DIR}")
        print("    Ready to process PDFs when available.")
        return

    print(f"✓ Found {len(pdf_files)} PDF files\n")

    # Process each PDF
    results = []
    success_count = 0
    fail_count = 0

    for pdf_path in pdf_files:
        print(f"Processing: {pdf_path.name}...")

        try:
            result = extract_pdf_pypdf2(pdf_path)

            # Save individual result
            output_file = OUTPUT_DIR / f"{pdf_path.stem}_pypdf2.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            # Save markdown version
            md_file = OUTPUT_DIR / f"{pdf_path.stem}_pypdf2.md"
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(f"# {result['pdf_file']}\n\n")
                f.write(f"**Extracted by**: PyPDF2\n")
                f.write(f"**Pages**: {result['num_pages']}\n")
                f.write(f"**Confidence**: {result['confidence_score']:.2%}\n\n")
                f.write("---\n\n")
                f.write(result['full_text'])

            results.append(result)
            success_count += 1
            print(f"  ✓ Success ({result['num_pages']} pages, {result['total_characters']:,} chars)")
            print(f"  → {output_file.name}")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()
            # Continue with next PDF

    # Summary report
    summary = {
        "solution": "pypdf2",
        "timestamp": datetime.utcnow().isoformat(),
        "total_pdfs": len(pdf_files),
        "successful": success_count,
        "failed": fail_count,
        "success_rate": success_count / len(pdf_files) if pdf_files else 0,
        "results": results
    }

    summary_file = OUTPUT_DIR / "summary_pypdf2.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary:")
    print(f"  Success: {success_count}")
    print(f"  Failed: {fail_count}")
    print(f"  Rate: {summary['success_rate']:.1%}")
    print(f"  Output: {OUTPUT_DIR}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
