#!/usr/bin/env python3
"""
(<PERSON>) Solution 2: Regex Pattern Matching

☑️ Fast rule-based extraction
☑️ No API costs
☑️ Deterministic results

Agent: B2
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict
import traceback

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority

# FULLCAPS CONFIG
MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution2_regex")


def parse_md_with_regex(md_path: Path) -> ICMemo:
    """
    Parse markdown using regex patterns.

    Raises:
        FileNotFoundError if MD doesn't exist then broken
        ValueError if extraction fails then broken
    """
    if not md_path.exists():
        raise FileNotFoundError(f"Markdown file not found: {md_path}")

    try:
        content = md_path.read_text(encoding='utf-8')

        # Extract date (MM/DD/YYYY format)
        date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', content)
        if date_match:
            date_str = date_match.group(1)
            memo_date = datetime.strptime(date_str, "%m/%d/%Y").date()
        else:
            # Try from filename
            filename_date = re.search(r'(\d{2}\.\d{2}\.\d{4})', md_path.name)
            if filename_date:
                memo_date = datetime.strptime(filename_date.group(1), "%m.%d.%Y").date()
            else:
                memo_date = datetime.now().date()

        # Extract deals - look for bullet points followed by deal info
        deals = []

        # Pattern: - DealName\n  1. Key Priorities
        deal_pattern = r'^- ([^\n]+)$\n^\s+1\.\s+Key Priorities'
        deal_matches = re.finditer(deal_pattern, content, re.MULTILINE)

        for match in deal_matches:
            deal_name = match.group(1).strip()

            # Extract priorities for this deal
            # Look for content between this deal and next deal or end
            deal_start = match.start()
            next_deal = re.search(deal_pattern, content[deal_start+1:], re.MULTILINE)

            if next_deal:
                deal_end = deal_start + 1 + next_deal.start()
            else:
                deal_end = len(content)

            deal_section = content[deal_start:deal_end]

            # Extract priorities (lines starting with a., b., c., etc.)
            priority_pattern = r'^\s+[a-z]\.\s+(.+)$'
            priority_matches = re.findall(priority_pattern, deal_section, re.MULTILINE)

            priorities = [
                Priority(description=p.strip(), category="Key Priorities")
                for p in priority_matches[:5]  # Limit to first 5
            ]

            deals.append(Deal(
                deal_name=deal_name,
                section="Deals in Process",
                priorities=priorities,
                notes=[]
            ))

        memo = ICMemo(
            memo_date=memo_date,
            source_file=md_path.name,
            deals=deals,
            extraction_method="regex_pattern_matching",
            confidence_score=0.75  # Medium confidence - regex can miss nuances
        )

        return memo

    except Exception as e:
        raise ValueError(f"Regex extraction failed: {e}")


def main():
    """Extract all markdown files using regex."""

    print("\n" + "="*60)
    print("Solution 2: Regex Pattern Matching")
    print("Agent: B2")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not MD_SOURCE_DIR.exists():
        print(f"✗ Source directory not found: {MD_SOURCE_DIR}")
        raise FileNotFoundError(f"Source not found: {MD_SOURCE_DIR}")

    md_files = list(MD_SOURCE_DIR.glob("*.md"))

    if not md_files:
        print(f"⚠️  No markdown files found")
        return

    print(f"✓ Found {len(md_files)} markdown files\n")

    results = []
    success_count = 0
    fail_count = 0

    for md_path in md_files:
        print(f"Processing: {md_path.name}...")

        try:
            memo = parse_md_with_regex(md_path)

            output_file = OUTPUT_DIR / f"{md_path.stem}_regex.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(memo.model_dump_json(indent=2))

            results.append(memo)
            success_count += 1
            print(f"  ✓ Extracted {len(memo.deals)} deals")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    summary = {
        "solution": "regex",
        "timestamp": datetime.utcnow().isoformat(),
        "total_files": len(md_files),
        "successful": success_count,
        "failed": fail_count,
        "total_deals_extracted": sum(len(m.deals) for m in results)
    }

    with open(OUTPUT_DIR / "summary_regex.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(md_files)} successful")
    print(f"Total deals extracted: {summary['total_deals_extracted']}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
