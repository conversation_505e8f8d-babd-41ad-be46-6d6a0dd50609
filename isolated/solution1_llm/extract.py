#!/usr/bin/env python3
"""
(<PERSON>) Solution 1: LLM Structured Output Parser

☑️ Uses Claude/GPT with structured JSON schema
☑️ Highest accuracy for complex extraction
☑️ Handles context and nuance

Agent: B1
"""

import sys
import json
import os
from pathlib import Path
from datetime import datetime
from typing import List
import traceback

try:
    import anthropic
except ImportError:
    print("ERROR: anthropic not installed. Run: pip install anthropic")
    sys.exit(1)

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo, Deal, Priority, FinancialMetric

# FULLCAPS CONFIG
MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution1_llm")
API_KEY = os.getenv("ANTHROPIC_API_KEY")


EXTRACTION_PROMPT = """You are an expert at extracting structured data from investment committee memos.

Extract the following from this IC memo:
1. All deal/company names
2. Key priorities for each deal
3. Strategic initiatives
4. Financial metrics (EBITDA, revenue, valuations if mentioned)
5. Notable activities (LOIs, QoE, M&A activities)

Return as JSON matching this schema:
{
  "memo_date": "YYYY-MM-DD",
  "deals": [
    {
      "deal_name": "string",
      "section": "Deals in Process|Portfolio|etc",
      "priorities": [{"description": "string", "category": "Key Priorities|Strategic Initiatives|etc"}],
      "notes": ["string"]
    }
  ]
}

Be thorough. Extract ALL deals and priorities."""


def parse_md_with_llm(md_path: Path) -> ICMemo:
    """
    Parse markdown using Claude API with structured output.

    Raises:
        FileNotFoundError if MD doesn't exist then broken
        EnvironmentError if API key missing then broken
        ValueError if extraction fails then broken
    """
    if not md_path.exists():
        raise FileNotFoundError(f"Markdown file not found: {md_path}")

    if not API_KEY:
        raise EnvironmentError("ANTHROPIC_API_KEY not set")

    try:
        # Read markdown
        md_content = md_path.read_text(encoding='utf-8')

        # Call Claude API
        client = anthropic.Anthropic(api_key=API_KEY)

        message = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=4096,
            messages=[
                {
                    "role": "user",
                    "content": f"{EXTRACTION_PROMPT}\n\n---\n\n{md_content}"
                }
            ]
        )

        # Parse JSON response
        response_text = message.content[0].text

        # Extract JSON from response (might be wrapped in markdown code blocks)
        if "```json" in response_text:
            json_start = response_text.find("```json") + 7
            json_end = response_text.find("```", json_start)
            json_str = response_text[json_start:json_end].strip()
        elif "```" in response_text:
            json_start = response_text.find("```") + 3
            json_end = response_text.find("```", json_start)
            json_str = response_text[json_start:json_end].strip()
        else:
            json_str = response_text.strip()

        data = json.loads(json_str)

        # Convert to ICMemo model
        deals = []
        for deal_data in data.get("deals", []):
            priorities = [
                Priority(
                    description=p["description"],
                    category=p.get("category")
                )
                for p in deal_data.get("priorities", [])
            ]

            deals.append(Deal(
                deal_name=deal_data["deal_name"],
                section=deal_data.get("section", "Unknown"),
                priorities=priorities,
                notes=deal_data.get("notes", [])
            ))

        memo = ICMemo(
            memo_date=datetime.strptime(data["memo_date"], "%Y-%m-%d").date(),
            source_file=md_path.name,
            deals=deals,
            extraction_method="llm_structured_claude",
            confidence_score=0.95  # High confidence for LLM extraction
        )

        return memo

    except Exception as e:
        raise ValueError(f"LLM extraction failed: {e}")


def main():
    """Extract all markdown files using LLM."""

    print("\n" + "="*60)
    print("Solution 1: LLM Structured Output (Claude)")
    print("Agent: B1")
    print("="*60 + "\n")

    if not API_KEY:
        print("⚠️  ANTHROPIC_API_KEY not set")
        print("    Skipping LLM extraction")
        return

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    if not MD_SOURCE_DIR.exists():
        print(f"✗ Source directory not found: {MD_SOURCE_DIR}")
        raise FileNotFoundError(f"Source not found: {MD_SOURCE_DIR}")

    md_files = list(MD_SOURCE_DIR.glob("*.md"))

    if not md_files:
        print(f"⚠️  No markdown files found")
        return

    print(f"✓ Found {len(md_files)} markdown files\n")

    results = []
    success_count = 0
    fail_count = 0

    for md_path in md_files:
        print(f"Processing: {md_path.name}...")

        try:
            memo = parse_md_with_llm(md_path)

            # Save as JSON
            output_file = OUTPUT_DIR / f"{md_path.stem}_llm.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(memo.model_dump_json(indent=2))

            results.append(memo)
            success_count += 1
            print(f"  ✓ Extracted {len(memo.deals)} deals")

        except Exception as e:
            fail_count += 1
            print(f"  ✗ Failed: {e}")
            traceback.print_exc()

    # Summary
    summary = {
        "solution": "llm_structured",
        "timestamp": datetime.utcnow().isoformat(),
        "total_files": len(md_files),
        "successful": success_count,
        "failed": fail_count,
        "total_deals_extracted": sum(len(m.deals) for m in results)
    }

    with open(OUTPUT_DIR / "summary_llm.json", 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\n{'='*60}")
    print(f"Summary: {success_count}/{len(md_files)} successful")
    print(f"Total deals extracted: {summary['total_deals_extracted']}")
    print(f"{'='*60}\n")


if __name__ == "__main__":
    main()
