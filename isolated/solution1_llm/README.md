# Solution 1: LLM Structured Output (Claude)

**Agent**: B1
**Method**: <PERSON> with JSON schema
**Accuracy**: Highest (95%+)

## Installation

```bash
cd solution1_llm/
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## API Key Setup

```bash
export ANTHROPIC_API_KEY="sk-ant-your-key-here"
```

## Usage

```bash
python extract.py
```

## Strengths
- Highest accuracy for complex extraction
- Understands context and nuance
- Handles ambiguous cases well
- Can extract implied information

## Weaknesses
- Requires API key (cost)
- Slower than rule-based methods
- Rate limits apply

## Output
- JSON: `../outputs/solution1_llm/{filename}_llm.json`
- Summary: `../outputs/solution1_llm/summary_llm.json`
