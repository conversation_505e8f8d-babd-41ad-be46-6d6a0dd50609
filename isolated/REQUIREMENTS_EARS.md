# PDF Extraction Requirements - EARS Format (<PERSON>)
## Easy Approach to Requirements Syntax

Generated: 2025-11-04
Agent: B0 (Primary Orchestrator)

---

## R.1 - Ubiquitous Requirements (Always Active)

### Core Functionality
- **R.1.1**: The system SHALL extract all text content from PDF documents with >98% accuracy
- **R.1.2**: The system SHALL extract all tables from PDF documents preserving structure
- **R.1.3**: The system SHALL extract financial metrics (EBITDA, revenue, valuations) with 100% accuracy
- **R.1.4**: The system SHALL preserve document metadata (dates, authors, memo numbers)
- **R.1.5**: The system SHALL process documents from `/Access IC Memos/` directory
- **R.1.6**: The system SHALL output results to `/isolated/outputs/` directory
- **R.1.7**: The system SHALL implement exactly 10 distinct PDF extraction solutions
- **R.1.8**: The system SHALL operate entirely within `/isolated/` directory boundaries
- **R.1.9**: The system SHALL NOT modify any files outside `/isolated/` directory
- **R.1.10**: The system SHALL maintain operation journal in `/isolated/journal-agentB0.md`

### Output Requirements
- **R.1.11**: The system SHALL generate markdown output for all extractions
- **R.1.12**: The system SHALL generate JSON structured data for all extractions
- **R.1.13**: The system SHALL include confidence scores for each extraction
- **R.1.14**: The system SHALL create comparison matrices across all 10 solutions
- **R.1.15**: The system SHALL generate validation reports with accuracy metrics

### Performance Requirements
- **R.1.16**: The system SHALL process minimum 10 PDFs per minute (per solution)
- **R.1.17**: The system SHALL complete full corpus extraction within 24 hours
- **R.1.18**: The system SHALL use <4GB RAM per extraction agent
- **R.1.19**: The system SHALL support PDFs up to 500 pages

---

## R.2 - Event-Driven Requirements (Triggered by Events)

### Extraction Events
- **R.2.1**: WHEN a PDF is processed, the system SHALL log start timestamp, file path, and solution name
- **R.2.2**: WHEN extraction completes, the system SHALL log end timestamp, output size, and success status
- **R.2.3**: WHEN extraction fails, the system SHALL retry with next available solution
- **R.2.4**: WHEN all solutions fail on a PDF, the system SHALL flag for manual review
- **R.2.5**: WHEN confidence score <95%, the system SHALL trigger validation workflow

### Comparison Events
- **R.2.6**: WHEN all 10 solutions complete on same PDF, the system SHALL generate comparison report
- **R.2.7**: WHEN discrepancies exceed 5% between solutions, the system SHALL trigger ensemble validation
- **R.2.8**: WHEN financial metric mismatch detected, the system SHALL halt and alert

### Validation Events
- **R.2.9**: WHEN extraction completes, the system SHALL validate against schema
- **R.2.10**: WHEN validation passes, the system SHALL persist to outputs directory
- **R.2.11**: WHEN validation fails, the system SHALL log error and re-queue

---

## R.3 - State-Driven Requirements (Conditional on System State)

### During Initialization
- **R.3.1**: WHILE initializing, the system SHALL verify all API keys are present
- **R.3.2**: WHILE initializing, the system SHALL download required models <5GB total
- **R.3.3**: WHILE initializing, the system SHALL create output directory structure
- **R.3.4**: WHILE initializing, the system SHALL verify PDF source directory exists

### During Extraction
- **R.3.5**: WHILE extracting, the system SHALL maintain isolation per solution
- **R.3.6**: WHILE extracting, the system SHALL stream progress to journal
- **R.3.7**: WHILE extracting, the system SHALL enforce memory limits
- **R.3.8**: WHILE extracting, the system SHALL handle graceful shutdown on interrupt

### During Validation
- **R.3.9**: WHILE validating, the system SHALL compare against ground truth if available
- **R.3.10**: WHILE validating, the system SHALL calculate accuracy metrics
- **R.3.11**: WHILE validating, the system SHALL detect structural anomalies
- **R.3.12**: WHILE validating, the system SHALL flag low-confidence extractions

---

## R.4 - Unwanted Behavior (Error Conditions)

### File System Protection
- **R.4.1**: IF write attempted outside `/isolated/`, THEN system SHALL reject and log error
- **R.4.2**: IF source PDF corrupted, THEN system SHALL skip and continue with next
- **R.4.3**: IF disk space <1GB, THEN system SHALL halt and alert
- **R.4.4**: IF output file exists, THEN system SHALL append timestamp suffix

### API Failures
- **R.4.5**: IF API key missing, THEN system SHALL skip that solution and continue
- **R.4.6**: IF API rate limit hit, THEN system SHALL exponential backoff retry
- **R.4.7**: IF API returns error, THEN system SHALL log and try alternative solution
- **R.4.8**: IF network timeout, THEN system SHALL retry 3 times then skip

### Data Quality
- **R.4.9**: IF extracted text empty, THEN system SHALL flag as extraction failure
- **R.4.10**: IF confidence score <50%, THEN system SHALL discard and try alternative
- **R.4.11**: IF table structure malformed, THEN system SHALL attempt repair or flag
- **R.4.12**: IF financial numbers contain letters, THEN system SHALL reject and re-extract

---

## R.5 - Optional Features (WHERE Clause)

### Advanced Features
- **R.5.1**: WHERE LlamaParse available, system SHOULD use premium mode for complex tables
- **R.5.2**: WHERE OCR required, system SHOULD use Marker or MinerU as primary
- **R.5.3**: WHERE tables detected, system SHOULD use Docling or Camelot for validation
- **R.5.4**: WHERE scanned PDF detected, system SHOULD trigger multimodal LLM approach

### Optimization Features
- **R.5.5**: WHERE GPU available, system SHOULD enable GPU acceleration for Marker/MinerU
- **R.5.6**: WHERE multiple PDFs queued, system SHOULD parallelize across solutions
- **R.5.7**: WHERE extraction cached, system SHOULD skip re-processing
- **R.5.8**: WHERE metadata available, system SHOULD use for validation hints

### Reporting Features
- **R.5.9**: WHERE all extractions complete, system SHOULD generate executive summary
- **R.5.10**: WHERE benchmarks run, system SHOULD create performance comparison charts
- **R.5.11**: WHERE errors occur, system SHOULD generate troubleshooting report
- **R.5.12**: WHERE journal complete, system SHOULD summarize key insights

---

## 10 SotA Solutions Identified

1. **LlamaParse** - Commercial, ~6s, strong structure, 9/10 rating
2. **Docling** - 97.9% table accuracy, open-source, TableFormer model
3. **Unstructured.io** - Semantic chunks, RAG-optimized, 1.29s
4. **PyMuPDF4LLM** - Fastest (0.12s), excellent markdown, speed+quality balance
5. **Marker** - Perfect layout, OCR capable, 11.3s, 1GB model
6. **MinerU** - Complex doc transformer, LLM-ready JSON/markdown
7. **PyPDF2** - Basic text extraction, lightweight, no dependencies
8. **pdfplumber** - Specialized table extraction, good for structured data
9. **Camelot** - Table-focused, 73% accuracy baseline, exportable formats
10. **Gemini 2.5 Pro** - Multimodal LLM approach, best all-around per benchmarks

---

## Success Criteria Matrix

| Criterion | Threshold | Measurement Method |
|-----------|-----------|-------------------|
| Text Accuracy | >98% | Character-level comparison |
| Table Accuracy | >95% | Cell-by-cell validation |
| Financial Accuracy | 100% | Exact numeric match |
| Coverage | 100% | All pages extracted |
| Confidence | >95% | Per-solution scoring |
| Speed | <1 min/PDF | Wall-clock time |
| Memory | <4GB | Peak RSS |
| Completeness | All 10 solutions | Implementation count |

---

## Validation Framework

### Level 1: Syntactic Validation
- Valid markdown/JSON output
- No parsing errors
- Complete file structure

### Level 2: Semantic Validation
- Financial metrics extracted
- Tables preserved with structure
- Metadata present

### Level 3: Cross-Solution Validation
- <5% variance between solutions
- Consensus on key data points
- Outlier detection

### Level 4: Ground Truth Validation (if available)
- Exact match on known values
- Structure alignment
- Completeness check

---

## Risk Mitigation

| Risk | Mitigation | Requirement |
|------|-----------|-------------|
| API cost overrun | Free tiers first, monitor usage | R.5.1, R.4.6 |
| Missing API keys | Graceful skip, document | R.4.5 |
| Isolation breach | Strict path validation | R.4.1 |
| Data loss | Atomic writes, versioning | R.4.4 |
| Performance degradation | Memory limits, timeouts | R.1.18, R.4.8 |
| Extraction failures | Multi-solution fallback | R.2.3, R.2.4 |

---

## Implementation Phases

### Phase 1: Foundation (Hours 0-2)
- Create directory structure ✓
- Set up journaling ✓
- Document requirements ✓
- Research solutions ✓

### Phase 2: Baseline LlamaParse (Hours 2-4)
- Install llamaparse
- Implement extraction pipeline
- Test on sample PDFs
- Validate outputs

### Phase 3: Parallel Implementation (Hours 4-24)
- Launch 10 sub-agents via ADW
- Implement each solution in isolation
- Run on full corpus
- Collect outputs

### Phase 4: Validation & Comparison (Hours 24-36)
- Cross-validate all extractions
- Generate comparison matrices
- Calculate accuracy metrics
- Identify best performers

### Phase 5: Integration & Reporting (Hours 36-48)
- Ensemble best solutions
- Generate final reports
- Document findings
- Create runbooks

---

*End of EARS Requirements Document*
