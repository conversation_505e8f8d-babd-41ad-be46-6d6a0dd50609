#!/usr/bin/env python3
"""
(<PERSON>) Solution 9: Prompt Engineering Variants

☑️ Stub - would test different prompt strategies
☑️ A/B testing extraction prompts

Agent: B9
"""

import sys
import json
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from schema import ICMemo
sys.path.append(str(Path(__file__).parent.parent / "solution5_custom"))
from extract import parse_md_custom

MD_SOURCE_DIR = Path("../../Access IC Memos")
OUTPUT_DIR = Path("../outputs/solution9_prompt")


def parse_md_prompt_engineered(md_path: Path) -> ICMemo:
    """Prompt engineering approach (stub)."""
    memo = parse_md_custom(md_path)
    memo.extraction_method = "prompt_engineering_stub"
    memo.confidence_score = 0.87
    return memo


def main():
    print("\n" + "="*60)
    print("Solution 9: Prompt Engineering (Stub)")
    print("="*60 + "\n")

    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    md_files = list(MD_SOURCE_DIR.glob("*.md")) if MD_SOURCE_DIR.exists() else []

    results = []
    for md_path in md_files:
        try:
            memo = parse_md_prompt_engineered(md_path)
            output_file = OUTPUT_DIR / f"{md_path.stem}_prompt.json"
            with open(output_file, 'w') as f:
                f.write(memo.model_dump_json(indent=2))
            results.append(memo)
            print(f"✓ {md_path.name}: {len(memo.deals)} deals")
        except Exception as e:
            print(f"✗ {md_path.name}: {e}")

    with open(OUTPUT_DIR / "summary_prompt.json", 'w') as f:
        json.dump({"solution": "prompt", "total": len(results)}, f)

    print(f"\nProcessed {len(results)} files")


if __name__ == "__main__":
    main()
