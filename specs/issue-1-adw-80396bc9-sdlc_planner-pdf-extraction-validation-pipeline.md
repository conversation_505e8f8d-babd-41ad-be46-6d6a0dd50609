# Feature: Complete PDF Extraction Pipeline with Validation

## Metadata
issue_number: `1`
adw_id: `80396bc9`
issue_json: `{"number":1,"title":"Complete PDF Extraction Pipeline with Validation","body":"# Complete PDF Extraction Pipeline with Validation\n\n## Problem\nThe current system processes markdown files, but the actual IC memos are in PDF format. We need a complete PDF extraction system with high-confidence validation.\n\n## Requirements\n\n### Core Functionality\n1. Extract ALL data from PDF files in `/Access IC Memos/` directory\n2. Use multiple PDF parsing libraries and compare results:\n   - PyPDF2 for basic text extraction\n   - pdfplumber for tables and structured data\n   - pdf2image + Tesseract OCR for scanned documents\n   - Camelot-py for complex tables\n3. Achieve >98% accuracy on financial metrics (EBITDA, revenue, valuations)\n4. Extract deals, companies, priorities with structure preserved\n\n### Validation System\n1. Implement double-blind validation using two git worktrees\n2. Compare extraction results between independent implementations\n3. Calculate confidence scores (must be >95% for production)\n4. Flag discrepancies >2% for manual review\n\n### Implementation Structure\n```\npdf_extraction/\n├── extractors/\n│   ├── __init__.py\n│   ├── pypdf2_extractor.py      # Basic text extraction\n│   ├── pdfplumber_extractor.py  # Tables and structured data\n│   ├── ocr_extractor.py         # Scanned document handling\n│   ├── camelot_extractor.py     # Complex table extraction\n│   └── ensemble_extractor.py    # Merge and vote on results\n├── validators/\n│   ├── __init__.py\n│   ├── double_blind.py          # Two-worktree validation\n│   ├── confidence_scorer.py     # Calculate extraction confidence\n│   └── discrepancy_analyzer.py  # Find and report differences\n├── pipeline/\n│   ├── __init__.py\n│   ├── orchestrator.py          # Main pipeline controller\n│   ├── parallel_processor.py    # Multi-agent parallel processing\n│   └── data_standardizer.py     # Normalize extracted data\n├── database/\n│   ├── __init__.py\n│   ├── schema.sql               # Database structure\n│   └── loader.py                # Load validated data to SQLite\n└── tests/\n    ├── __init__.py\n    ├── test_extraction_accuracy.py\n    ├── test_validation_system.py\n    └── fixtures/                # Test PDFs with known values\n        ├── sample_memo.pdf\n        └── expected_output.json\n```\n\n### Technical Approach\n\n#### Phase 1: Setup and Discovery\n```python\n# Install required libraries\npip install PyPDF2 pdfplumber pdf2image pytesseract camelot-py[cv] tabula-py\n\n# Find all PDF files\npdf_files = glob.glob('/Access IC Memos/**/*.pdf', recursive=True)\n```\n\n#### Phase 2: Multi-Library Extraction\n```python\ndef extract_with_all_methods(pdf_path):\n    results = {}\n\n    # Method 1: PyPDF2\n    results['pypdf2'] = extract_with_pypdf2(pdf_path)\n\n    # Method 2: pdfplumber\n    results['pdfplumber'] = extract_with_pdfplumber(pdf_path)\n\n    # Method 3: OCR\n    results['ocr'] = extract_with_ocr(pdf_path)\n\n    # Method 4: Camelot for tables\n    results['camelot'] = extract_tables_with_camelot(pdf_path)\n\n    return results\n\ndef ensemble_merge(results):\n    \"\"\"Merge results using voting and confidence scoring\"\"\"\n    merged = {}\n    for field in get_all_fields(results):\n        values = [r.get(field) for r in results.values() if field in r]\n        merged[field] = {\n            'value': majority_vote(values),\n            'confidence': calculate_agreement(values),\n            'sources': len([v for v in values if v])\n        }\n    return merged\n```\n\n#### Phase 3: Double-Blind Validation\n```bash\n# Worktree A: Primary extraction\ngit worktree add trees/extraction_a\ncd trees/extraction_a\npython pdf_extraction/pipeline/orchestrator.py --method primary\n\n# Worktree B: Validation extraction\ngit worktree add trees/extraction_b\ncd trees/extraction_b\npython pdf_extraction/pipeline/orchestrator.py --method validation\n\n# Compare results\npython pdf_extraction/validators/double_blind.py \\\n    --primary trees/extraction_a/output.json \\\n    --validation trees/extraction_b/output.json\n```\n\n#### Phase 4: Parallel Processing\n```python\nfrom concurrent.futures import ProcessPoolExecutor\n\nclass ParallelExtractor:\n    def __init__(self):\n        self.agents = [\n            TextExtractionAgent(),\n            TableExtractionAgent(),\n            FinancialMetricsAgent(),\n            EntityExtractionAgent(),\n            DateExtractionAgent()\n        ]\n\n    def process(self, pdf_path):\n        with ProcessPoolExecutor(max_workers=5) as executor:\n            futures = [executor.submit(agent.extract, pdf_path)\n                      for agent in self.agents]\n            results = [f.result() for f in futures]\n        return self.merge_results(results)\n```\n\n### Success Criteria\n- [ ] All PDF files in /Access IC Memos/ successfully processed\n- [ ] 100% of text content extracted\n- [ ] Tables extracted with structure preserved\n- [ ] Financial metrics accuracy >98%\n- [ ] All deals and companies identified\n- [ ] Confidence score >95% for each field\n- [ ] Validation shows <2% discrepancy\n- [ ] Processing time <30 seconds per PDF\n- [ ] All data loaded into SQLite database\n- [ ] Natural language queries working on extracted data\n\n### Testing Strategy\n1. Create test PDFs with known content\n2. Run extraction pipeline\n3. Compare against expected values\n4. Measure accuracy percentages\n5. Test edge cases (scanned, rotated, multi-column)\n6. Validate financial calculations\n7. Test query interface with extracted data\n\n### Deliverables\n1. Complete PDF extraction pipeline\n2. Validation system with confidence scoring\n3. SQLite database with extracted data\n4. Test suite with >90% coverage\n5. Documentation of extraction methodology\n6. Performance benchmarks\n7. Natural language query interface for extracted data\n"}`

## Feature Description
This feature implements a comprehensive PDF extraction and validation pipeline to process Investment Committee (IC) memos from PDF format into a structured SQLite database. The system uses multiple PDF parsing libraries (PyPDF2, pdfplumber, OCR, Camelot) in an ensemble approach to maximize accuracy, particularly for financial metrics which must achieve >98% accuracy. A double-blind validation system using git worktrees ensures data quality by comparing independent extraction runs and calculating confidence scores (target >95%). The extracted data integrates seamlessly with the existing natural language query interface, enabling users to ask questions about deals, companies, financial metrics, and priorities across all IC memos.

## User Story
As an investment committee member
I want to query information from PDF IC memos using natural language
So that I can quickly access deal information, financial metrics, and priorities without manually searching through documents

## Problem Statement
The current system is designed to work with markdown files, but the actual IC memos are in PDF format located in `/Access IC Memos/`. Processing PDFs requires specialized extraction techniques because:
1. PDFs contain complex layouts with tables, multi-column text, and structured financial data
2. Some PDFs may be scanned images requiring OCR
3. Financial metrics (EBITDA, revenue, valuations) must be extracted with >98% accuracy to be trustworthy
4. There is no validation mechanism to ensure extraction accuracy and catch errors
5. Multiple PDF parsing libraries have different strengths, requiring an ensemble approach

Without a robust PDF extraction pipeline with validation, the natural language query system cannot access the actual source documents, making it incomplete and unusable for real investment committee workflows.

## Solution Statement
We will build a multi-stage PDF extraction and validation pipeline that:

1. **Extraction Phase**: Uses four specialized PDF parsing libraries (PyPDF2, pdfplumber, pdf2image+OCR, Camelot) in parallel to extract text, tables, and structured data. An ensemble method votes on extracted values and calculates confidence scores based on cross-library agreement.

2. **Validation Phase**: Implements double-blind validation using two independent git worktrees that run the extraction pipeline separately and compare results. Discrepancies >2% are flagged for manual review, and overall confidence scores must exceed 95% before data is committed to production.

3. **Integration Phase**: Extracted and validated data is loaded into the existing SQLite database schema (memos, deals, companies, priorities tables) using the current Pydantic models, ensuring seamless integration with the natural language query interface.

4. **Quality Assurance**: Comprehensive test suite validates extraction accuracy using PDFs with known ground truth values, measures financial metric accuracy (target >98%), and benchmarks processing performance (target <30 seconds per PDF).

This approach ensures high-confidence data extraction while maintaining compatibility with the existing application architecture.

## Relevant Files
Use these files to implement the feature:

- **app/server/ingestion.py** - Current markdown ingestion module with LLM-based structured extraction using Pydantic models (ICMemoData, Deal, Company). This file provides the pattern for database insertion and schema management. We will extend this to support PDF input and integrate ensemble extraction results.

- **app/server/core/query_generator.py** - Natural language to SQL query generator with investment domain context. After PDF extraction, queries will work on the newly ingested PDF data using the same database schema.

- **app/server/server.py** - FastAPI server with `/api/ingest` endpoint. We'll add new endpoints for PDF upload, extraction status monitoring, and validation report retrieval.

- **app/server/core/__init__.py** - Package initialization. We'll add the new pdf_extraction module here.

- **.claude/commands/test_e2e.md** - E2E test runner framework using Playwright. This will be referenced when creating PDF extraction validation tests.

- **.claude/commands/e2e/test_basic_query.md** - Example E2E test structure. Our PDF extraction E2E test will follow this pattern to validate end-to-end PDF ingestion and query functionality.

### New Files

- **app/server/pdf_extraction/__init__.py** - Package initialization for PDF extraction module

- **app/server/pdf_extraction/extractors/__init__.py** - Extractor submodule initialization

- **app/server/pdf_extraction/extractors/pypdf2_extractor.py** - PyPDF2-based text extraction for basic PDF text

- **app/server/pdf_extraction/extractors/pdfplumber_extractor.py** - pdfplumber-based extraction for tables and structured data

- **app/server/pdf_extraction/extractors/ocr_extractor.py** - OCR-based extraction for scanned PDFs using pdf2image and Tesseract

- **app/server/pdf_extraction/extractors/camelot_extractor.py** - Camelot-based extraction for complex table structures

- **app/server/pdf_extraction/extractors/ensemble_extractor.py** - Ensemble method that merges results from all extractors using voting and confidence scoring

- **app/server/pdf_extraction/validators/__init__.py** - Validator submodule initialization

- **app/server/pdf_extraction/validators/double_blind.py** - Double-blind validation comparing two independent extraction runs

- **app/server/pdf_extraction/validators/confidence_scorer.py** - Calculates confidence scores based on cross-extractor agreement

- **app/server/pdf_extraction/validators/discrepancy_analyzer.py** - Identifies and reports extraction discrepancies for manual review

- **app/server/pdf_extraction/pipeline/__init__.py** - Pipeline submodule initialization

- **app/server/pdf_extraction/pipeline/orchestrator.py** - Main pipeline controller that coordinates extraction and validation workflows

- **app/server/pdf_extraction/pipeline/parallel_processor.py** - Parallel processing using ProcessPoolExecutor for multiple PDFs

- **app/server/pdf_extraction/pipeline/data_standardizer.py** - Normalizes extracted data to match database schema

- **app/server/pdf_extraction/database/__init__.py** - Database submodule initialization

- **app/server/pdf_extraction/database/schema.sql** - SQL schema definitions (extends existing schema if needed)

- **app/server/pdf_extraction/database/loader.py** - Loads validated extraction results into SQLite database

- **app/server/pdf_extraction/tests/__init__.py** - Test submodule initialization

- **app/server/pdf_extraction/tests/test_extraction_accuracy.py** - Tests extraction accuracy against known ground truth PDFs

- **app/server/pdf_extraction/tests/test_validation_system.py** - Tests double-blind validation and confidence scoring

- **app/server/pdf_extraction/tests/fixtures/sample_memo.pdf** - Sample test PDF with known content

- **app/server/pdf_extraction/tests/fixtures/expected_output.json** - Expected extraction results for sample PDF

- **scripts/run_pdf_extraction.sh** - Shell script to run PDF extraction pipeline

- **scripts/run_validation.sh** - Shell script to run double-blind validation

- **.claude/commands/e2e/test_pdf_extraction.md** - E2E test validating complete PDF extraction and query workflow

## Implementation Plan

### Phase 1: Foundation
Set up the PDF extraction module structure, install dependencies, and create base extractors. This phase establishes the foundational components without implementing complex validation logic. We'll create the directory structure, add blank `__init__.py` files, install required PDF parsing libraries (PyPDF2, pdfplumber, pdf2image, pytesseract, camelot-py), and implement basic extractors for each library that return raw text and table data.

### Phase 2: Core Implementation
Implement the ensemble extraction system, confidence scoring, and double-blind validation. This phase builds the core intelligence of the system by combining results from multiple extractors, calculating confidence scores based on agreement, implementing the double-blind validation workflow using git worktrees, and creating the data standardizer to normalize extracted data into the existing Pydantic models (ICMemoData, Deal, Company).

### Phase 3: Integration
Integrate the PDF extraction pipeline with the existing ingestion system and FastAPI server. Extend `ingestion.py` to support PDF input alongside markdown, add new API endpoints for PDF upload and extraction monitoring, create database loader to insert validated data into SQLite, and implement end-to-end tests validating PDF ingestion through natural language queries.

## Step by Step Tasks
IMPORTANT: Execute every step in order, top to bottom.

### 1. Create PDF Extraction Module Structure
- Create directory: `app/server/pdf_extraction/`
- Create subdirectories: `extractors/`, `validators/`, `pipeline/`, `database/`, `tests/`, `tests/fixtures/`
- Add blank `__init__.py` files to all package directories
- Register the new module in `app/server/pyproject.toml` if using uv

### 2. Install PDF Processing Dependencies
- Run `cd app/server && uv add PyPDF2 pdfplumber pdf2image pytesseract camelot-py opencv-python tabula-py pillow`
- Verify Tesseract OCR is installed on system (required for pytesseract): `tesseract --version`
- If Tesseract not found, add installation instructions to README and provide installation command for macOS: `brew install tesseract`
- Create `requirements_pdf.txt` listing all PDF-related dependencies for documentation

### 3. Implement PyPDF2 Extractor
- Create `app/server/pdf_extraction/extractors/pypdf2_extractor.py`
- Implement `PyPDF2Extractor` class with `extract_text(pdf_path: str) -> str` method
- Extract full text content from PDF pages
- Handle common PyPDF2 exceptions and return error indicators
- Add basic test in `pdf_extraction/tests/test_extraction_accuracy.py` using a simple test PDF

### 4. Implement pdfplumber Extractor
- Create `app/server/pdf_extraction/extractors/pdfplumber_extractor.py`
- Implement `PdfplumberExtractor` class with methods: `extract_text(pdf_path: str) -> str` and `extract_tables(pdf_path: str) -> List[pd.DataFrame]`
- Extract text with better structure preservation than PyPDF2
- Extract tables as structured data (pandas DataFrames)
- Handle multi-column layouts and complex page structures
- Add test validating table extraction accuracy

### 5. Implement OCR Extractor
- Create `app/server/pdf_extraction/extractors/ocr_extractor.py`
- Implement `OCRExtractor` class using pdf2image and pytesseract
- Convert PDF pages to images using `pdf2image.convert_from_path()`
- Apply OCR to each image using `pytesseract.image_to_string()`
- Concatenate results from all pages
- Add configuration for OCR language and DPI settings
- Add test using a scanned PDF image

### 6. Implement Camelot Extractor
- Create `app/server/pdf_extraction/extractors/camelot_extractor.py`
- Implement `CamelotExtractor` class for complex table extraction
- Use both `camelot.read_pdf(flavor='lattice')` for bordered tables and `camelot.read_pdf(flavor='stream')` for borderless tables
- Return tables as pandas DataFrames with extraction confidence scores
- Handle PDFs with multiple tables per page
- Add test validating complex financial table extraction

### 7. Implement Ensemble Extractor
- Create `app/server/pdf_extraction/extractors/ensemble_extractor.py`
- Implement `EnsembleExtractor` class that coordinates all extractors
- Method `extract_all(pdf_path: str) -> Dict[str, Any]` runs all extractors in parallel using ThreadPoolExecutor
- Method `merge_results(results: Dict) -> Dict` implements voting logic for text fields
- Method `select_best_tables(table_results: Dict) -> List[pd.DataFrame]` selects tables with highest confidence
- Calculate per-field confidence scores based on cross-extractor agreement
- Return structured result with confidence metadata
- Add comprehensive test validating ensemble voting logic

### 8. Implement Confidence Scorer
- Create `app/server/pdf_extraction/validators/confidence_scorer.py`
- Implement `ConfidenceScorer` class with method `calculate_confidence(extraction_results: Dict) -> float`
- Score text agreement using string similarity (Levenshtein distance)
- Score numerical agreement (revenue, EBITDA) with exact match and percentage tolerance
- Aggregate scores across all fields with weighted importance (financial metrics weighted higher)
- Return overall confidence percentage and per-field confidence breakdown
- Add test with known multi-extractor results to validate scoring accuracy

### 9. Implement Data Standardizer
- Create `app/server/pdf_extraction/pipeline/data_standardizer.py`
- Implement `DataStandardizer` class that converts raw extraction results to Pydantic models
- Method `standardize_to_ic_memo_data(raw_data: Dict) -> ICMemoData` converts to existing schema
- Use LLM (following existing pattern from `ingestion.py`) to parse unstructured text into structured fields
- Extract deals, companies, financial metrics, priorities, and initiatives
- Validate all extracted data against Pydantic models before returning
- Add test validating conversion from raw extraction to ICMemoData

### 10. Implement Orchestrator
- Create `app/server/pdf_extraction/pipeline/orchestrator.py`
- Implement `ExtractionOrchestrator` class as main pipeline controller
- Method `extract_single_pdf(pdf_path: str, method: str = 'primary') -> ICMemoData` orchestrates full extraction workflow
- Steps: run ensemble extraction → calculate confidence → standardize data → validate confidence threshold (>95%)
- If confidence < 95%, raise `LowConfidenceError` with details
- Method `extract_multiple_pdfs(pdf_dir: str) -> List[ICMemoData]` processes directory of PDFs
- Add CLI interface using Click for running orchestrator from command line
- Add test validating full orchestration workflow

### 11. Implement Double-Blind Validator
- Create `app/server/pdf_extraction/validators/double_blind.py`
- Implement `DoubleBlindValidator` class for comparing two independent extractions
- Method `compare_extractions(primary: ICMemoData, validation: ICMemoData) -> ValidationReport` compares all fields
- Calculate discrepancy percentages for financial metrics
- Flag discrepancies >2% for manual review
- Generate comprehensive validation report with pass/fail status
- Add test using two intentionally different extraction results

### 12. Implement Discrepancy Analyzer
- Create `app/server/pdf_extraction/validators/discrepancy_analyzer.py`
- Implement `DiscrepancyAnalyzer` class with method `analyze(primary: Dict, validation: Dict) -> List[Discrepancy]`
- Identify all fields with disagreements
- Categorize discrepancies by severity: critical (>10%), warning (2-10%), minor (<2%)
- Generate detailed reports showing exact differences with context
- Suggest most likely correct value based on extractor confidence
- Add test validating discrepancy detection and categorization

### 13. Implement Database Loader
- Create `app/server/pdf_extraction/database/loader.py`
- Implement `DatabaseLoader` class with method `load_ic_memo_data(data: ICMemoData, db_path: str)`
- Reuse existing database insertion logic from `ingestion.py._insert_memo_data()`
- Add transaction support with rollback on errors
- Track extraction metadata (extraction date, confidence score, validation status)
- Add method `bulk_load(data_list: List[ICMemoData], db_path: str)` for batch loading
- Add test validating database insertion and data integrity

### 14. Implement Parallel Processor
- Create `app/server/pdf_extraction/pipeline/parallel_processor.py`
- Implement `ParallelProcessor` class using ProcessPoolExecutor
- Method `process_directory(pdf_dir: str, max_workers: int = 4) -> List[ICMemoData]` processes multiple PDFs concurrently
- Track progress with rich progress bar
- Aggregate confidence scores and extraction statistics
- Handle failures gracefully (continue processing even if some PDFs fail)
- Add test validating parallel processing with multiple test PDFs

### 15. Create Validation Shell Scripts
- Create `scripts/run_validation.sh` for double-blind validation workflow
- Script creates two git worktrees: `trees/extraction_primary` and `trees/extraction_validation`
- Runs extraction pipeline in each worktree independently
- Compares results using `DoubleBlindValidator`
- Outputs validation report with pass/fail status
- Cleans up worktrees after validation
- Add documentation in script comments explaining the double-blind methodology

### 16. Create Test Fixtures
- Create `app/server/pdf_extraction/tests/fixtures/sample_memo.pdf` with known content (you can convert a markdown memo to PDF or use an actual IC memo)
- Create `app/server/pdf_extraction/tests/fixtures/expected_output.json` with ground truth extraction results
- Document expected values for all key fields: memo_date, deals, companies, financial metrics
- Include edge cases: multi-column layout, complex tables, financial notation variations
- Add README in fixtures/ explaining how to add new test cases

### 17. Implement Extraction Accuracy Tests
- Create `app/server/pdf_extraction/tests/test_extraction_accuracy.py`
- Test each extractor individually against sample PDF
- Test ensemble extractor achieving >98% accuracy on financial metrics
- Test extraction of all required fields: deals, companies, revenue, EBITDA, status, priorities
- Test edge cases: scanned PDFs, rotated pages, multi-column layouts
- Validate extraction completes within performance target (<30 seconds per PDF)
- Calculate and report accuracy percentages for each field type

### 18. Implement Validation System Tests
- Create `app/server/pdf_extraction/tests/test_validation_system.py`
- Test confidence scorer with various agreement levels
- Test double-blind validator comparing identical and different extractions
- Test discrepancy analyzer identifying and categorizing differences
- Test validation workflow end-to-end with worktrees
- Verify confidence threshold enforcement (>95% required)
- Verify discrepancy flagging (>2% triggers manual review)

### 19. Extend Ingestion Module for PDFs
- Modify `app/server/ingestion.py` to add PDF support
- Add method `ICMemoIngestion.ingest_pdf(pdf_path: str)` that uses the PDF extraction pipeline
- Add method `ICMemoIngestion.ingest_pdfs_from_directory(pdf_dir: str)` for batch PDF ingestion
- Integrate with existing database schema and insertion logic
- Add configuration option to choose between markdown and PDF ingestion
- Maintain backward compatibility with existing markdown ingestion

### 20. Add PDF Upload API Endpoint
- Modify `app/server/server.py` to add `/api/upload_pdf` endpoint
- Accept PDF file upload using FastAPI `UploadFile`
- Save uploaded PDF to temporary location
- Trigger PDF extraction pipeline asynchronously
- Return extraction status with job ID for monitoring
- Add validation for file type (PDF only) and size limits
- Add error handling and user-friendly error messages

### 21. Add Extraction Status API Endpoint
- Add `/api/extraction_status/{job_id}` endpoint to monitor extraction progress
- Return status: pending, processing, completed, failed
- Include confidence score and validation status when completed
- Return extracted record count (memos, deals, companies)
- Add error details if extraction failed
- Store extraction jobs in memory or simple job tracking table

### 22. Create PDF Extraction Shell Script
- Create `scripts/run_pdf_extraction.sh` for running extraction pipeline
- Accept PDF directory path as argument
- Run `ExtractionOrchestrator` on all PDFs in directory
- Display progress with rich progress bar
- Output summary statistics: total PDFs, successful extractions, failed extractions, average confidence
- Save extraction report to file
- Make script executable with `chmod +x`

### 23. Create E2E Test for PDF Extraction
- Create `.claude/commands/e2e/test_pdf_extraction.md` following the pattern from `test_basic_query.md`
- Test steps:
  1. Upload a test PDF via API
  2. Monitor extraction status until complete
  3. Verify extraction succeeded with confidence >95%
  4. Query extracted data using natural language
  5. Verify query results match expected data from PDF
- Include screenshots at each major step
- Validate end-to-end workflow from PDF upload to natural language query
- Include success criteria and verification steps

### 24. Update Documentation
- Update `README.md` with PDF extraction setup instructions
- Document required dependencies (Tesseract OCR installation)
- Add usage examples for PDF ingestion
- Document validation workflow and confidence scoring
- Add troubleshooting section for common PDF extraction issues
- Document API endpoints for PDF upload and extraction monitoring
- Add performance benchmarks and accuracy metrics

### 25. Run Validation Commands
- Execute all validation commands listed in "Validation Commands" section below
- Verify zero regressions in existing functionality
- Verify PDF extraction tests pass with >90% coverage
- Run E2E test validating complete PDF workflow
- Verify server starts without errors
- Verify frontend builds successfully
- Document any issues found and resolve before completion

## Testing Strategy

### Unit Tests
- **Extractor Tests**: Each extractor (PyPDF2, pdfplumber, OCR, Camelot) tested individually with sample PDFs containing known content. Validate text extraction, table extraction, and error handling.
- **Ensemble Tests**: Test voting logic with intentionally conflicting results from different extractors. Verify confidence scoring accurately reflects agreement levels.
- **Confidence Scorer Tests**: Test with various agreement scenarios (100% match, 75% match, 50% match, 0% match). Verify financial metrics weighted higher than text fields.
- **Validator Tests**: Test double-blind comparison with identical extractions (should pass), slightly different extractions (should warn), and significantly different extractions (should fail).
- **Data Standardizer Tests**: Test conversion from raw extraction dictionaries to Pydantic models. Verify all fields mapped correctly and validation errors raised for invalid data.
- **Database Loader Tests**: Test insertion of ICMemoData into SQLite. Verify foreign key relationships, transaction rollback on errors, and duplicate handling.
- **Orchestrator Tests**: Test full pipeline with mock extractors to verify workflow logic without actually processing PDFs.

### Edge Cases
- **Scanned PDFs**: PDFs that are images of documents (require OCR). Test that OCR extractor is invoked and text is extracted.
- **Rotated Pages**: PDFs with pages rotated 90/180/270 degrees. Test that extractors handle rotation correctly.
- **Multi-Column Layouts**: PDFs with 2-3 column layouts common in reports. Test that text extraction maintains reading order.
- **Complex Tables**: Tables with merged cells, nested headers, and irregular structures. Test that Camelot extractor handles complexity.
- **Mixed Content**: PDFs with text, tables, images, and charts. Test that extractors process all relevant content types.
- **Corrupted PDFs**: PDFs with errors or corruption. Test graceful error handling and informative error messages.
- **Large PDFs**: PDFs with 50+ pages. Test performance stays within target (<30 seconds) and memory usage is reasonable.
- **Financial Notation Variations**: Numbers with different formatting ($10M vs $10,000,000 vs 10mm). Test standardization and accurate extraction.
- **Missing Data**: PDFs missing some expected fields (e.g., no EBITDA listed). Test that extraction continues and marks fields as null.
- **Multiple Deals Per PDF**: PDFs discussing 5-10 different deals. Test that all deals are extracted and correctly associated with companies.

## Acceptance Criteria
- All PDF files in `/Access IC Memos/` directory are discovered and processed successfully (0% failure rate)
- Text extraction achieves 100% content coverage (no missing pages or sections)
- Table structures are preserved with row and column relationships intact
- Financial metrics (revenue, EBITDA, valuations) extracted with >98% accuracy compared to ground truth
- All deals and associated companies are identified and linked correctly in database
- Every extracted field has a confidence score calculated based on cross-extractor agreement
- Overall extraction confidence score exceeds 95% for production database commits
- Double-blind validation shows <2% discrepancy between independent extraction runs
- Processing time per PDF is <30 seconds on standard hardware (measured and benchmarked)
- All validated data is successfully loaded into SQLite database with proper foreign key relationships
- Natural language queries work correctly on PDF-extracted data (test with 10 sample queries)
- Test suite achieves >90% code coverage for PDF extraction module
- E2E test passes validating upload → extract → query workflow
- API endpoints for PDF upload and extraction status work correctly and handle errors gracefully
- Documentation is complete with setup instructions, usage examples, and troubleshooting guide
- Zero regressions in existing markdown ingestion or natural language query functionality

## Validation Commands
Execute every command to validate the feature works correctly with zero regressions.

Read `.claude/commands/test_e2e.md`, then read and execute your new E2E `.claude/commands/e2e/test_pdf_extraction.md` test file to validate this functionality works.

- `cd app/server && uv run pytest pdf_extraction/tests/ -v --cov=pdf_extraction --cov-report=term-missing` - Run PDF extraction tests with coverage report
- `cd app/server && uv run python pdf_extraction/tests/test_extraction_accuracy.py` - Run extraction accuracy validation directly
- `cd app/server && uv run python pdf_extraction/tests/test_validation_system.py` - Run validation system tests directly
- `cd app/server && uv run python pdf_extraction/pipeline/orchestrator.py --pdf /path/to/sample.pdf --output test_output.json` - Test orchestrator CLI with sample PDF
- `bash scripts/run_pdf_extraction.sh "/Users/<USER>/Code3b/ic_memo_nl_sql/Access IC Memos"` - Run full PDF extraction on IC memos directory
- `bash scripts/run_validation.sh` - Run double-blind validation workflow with worktrees
- `cd app/server && uv run pytest` - Run all server tests to validate zero regressions
- `cd app/client && bun tsc --noEmit` - Run frontend TypeScript checks to validate zero regressions
- `cd app/client && bun run build` - Run frontend build to validate zero regressions
- `cd app/server && uv run python server.py &` then test PDF upload endpoint with curl: `curl -F "file=@sample.pdf" http://localhost:8000/api/upload_pdf` - Validate PDF upload API
- Query validation: Start server, run sample query "Show me deals with EBITDA over $5M" and verify results include PDF-extracted data

## Notes

### Dependencies Added
The following packages were added using `uv add`:
- **PyPDF2**: Basic PDF text extraction (simple, widely compatible)
- **pdfplumber**: Advanced text and table extraction with layout awareness
- **pdf2image**: Convert PDF pages to images (required for OCR)
- **pytesseract**: Python wrapper for Tesseract OCR engine
- **camelot-py[cv]**: Advanced table extraction with OpenCV support
- **opencv-python**: Required by camelot-py for image processing
- **tabula-py**: Alternative table extraction library (backup option)
- **pillow**: Image processing library (required by pdf2image)

Additionally, **Tesseract OCR** must be installed at the system level:
- macOS: `brew install tesseract`
- Ubuntu: `apt-get install tesseract-ocr`
- Windows: Download installer from GitHub

### Current System Context
The existing system has:
1. **Markdown ingestion pipeline** (`ingestion.py`) using LLM-based structured extraction with Pydantic models
2. **SQLite database schema** with tables: memos, deals, companies, priorities
3. **Natural language query interface** that works on the database
4. **FastAPI server** with upload and query endpoints

The PDF extraction pipeline integrates with these existing components by:
- Reusing the same Pydantic models (ICMemoData, Deal, Company)
- Using the same database schema and insertion methods
- Extending the ingestion module to support PDF input
- Adding new API endpoints for PDF-specific workflows

### Design Decisions
1. **Ensemble Approach**: Multiple extractors are used because different PDF libraries have different strengths. PyPDF2 is fast but basic, pdfplumber handles layout better, OCR works on scanned docs, and Camelot excels at tables. Voting on results maximizes accuracy.

2. **Double-Blind Validation**: Using git worktrees for truly independent extraction runs ensures validation isn't just testing the same code path twice. This catches bugs in the extraction logic itself.

3. **Confidence Scoring**: Cross-extractor agreement is the best indicator of extraction quality without manual ground truth labeling. Higher confidence means more extractors agreed, indicating the result is likely correct.

4. **>98% Financial Accuracy Requirement**: Financial metrics are the most critical data for investment decisions. High accuracy is non-negotiable. We achieve this through ensemble voting and validation.

5. **Asynchronous API Pattern**: PDF extraction can take 10-30 seconds per document. Using job-based async API prevents timeouts and provides progress monitoring.

### Future Considerations
- **Incremental Updates**: Currently processes all PDFs each run. Future enhancement could track which PDFs are new/modified and only process those.
- **Machine Learning Enhancement**: Could train a custom model to improve extraction accuracy for IC memo specific layouts and terminology.
- **Distributed Processing**: For very large PDF collections (1000+ documents), could distribute processing across multiple machines.
- **Real-time Monitoring Dashboard**: Build admin UI showing extraction progress, confidence scores, and validation reports.
- **Custom Tesseract Training**: Train Tesseract on IC memo fonts and layouts for better OCR accuracy on scanned documents.
- **Table Structure Validation**: Add semantic validation that extracted tables have expected columns and data types for financial tables.
